import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import matplotlib.ticker as ticker
import numpy as np

# 读取新数据文件
file_path1 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx'
file_path2 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx'
file_path3 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx'
file_path4 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'

data1 = pd.read_excel(file_path1)
data2 = pd.read_excel(file_path2)
data3 = pd.read_excel(file_path3)
data4 = pd.read_excel(file_path4)

# 过滤数据（如果需要）
data1 = data1[data1['Step'] < 10000]
data2 = data2[data2['Step'] < 10000]
data3 = data3[data3['Step'] < 10000]
data4 = data4[data4['Step'] < 10000]

datasets = [data1, data2, data3, data4]
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']
colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']

print("=== 新数据起点分析 ===")
print("原始起点Value:")
original_starts = []
for data, label in zip(datasets, labels):
    start_value = data['Value'].iloc[0]
    original_starts.append(start_value)
    print(f"{label}: {start_value:.2f}")

# 设置统一起点值
TARGET_START = 500.0  # 您可以调整这个值

print(f"\n目标统一起点: {TARGET_START}")

# 方法1: 直接平移到统一起点
def method1_direct_shift(datasets, labels, target_start):
    """直接平移所有数据到统一起点"""
    shifted_datasets = []
    print(f"\n=== 方法1: 直接平移到 {target_start} ===")
    
    for data, label in zip(datasets, labels):
        data_shifted = data.copy()
        original_start = data['Value'].iloc[0]
        offset = target_start - original_start
        
        # 平移所有Value
        data_shifted['Value'] = data['Value'] + offset
        shifted_datasets.append(data_shifted)
        
        print(f"{label}: 原始={original_start:.2f}, 偏移={offset:.2f}, 新起点={data_shifted['Value'].iloc[0]:.2f}")
    
    return shifted_datasets

# 方法2: 相对于起点的百分比变化
def method2_percentage_change(datasets, labels, base_value=500):
    """转换为相对于起点的百分比变化"""
    percentage_datasets = []
    print(f"\n=== 方法2: 相对变化（基准值 {base_value}）===")
    
    for data, label in zip(datasets, labels):
        data_pct = data.copy()
        start_value = data['Value'].iloc[0]
        
        # 计算相对于起点的变化百分比，然后加到基准值上
        percentage_change = (data['Value'] - start_value) / start_value
        data_pct['Value'] = base_value + (percentage_change * base_value)
        
        percentage_datasets.append(data_pct)
        print(f"{label}: 起点={start_value:.2f}, 新起点={data_pct['Value'].iloc[0]:.2f}")
    
    return percentage_datasets

# 选择使用哪种方法
USE_METHOD = 1  # 1=直接平移, 2=百分比变化

if USE_METHOD == 1:
    processed_datasets = method1_direct_shift(datasets, labels, TARGET_START)
    method_name = "Direct Shift"
else:
    processed_datasets = method2_percentage_change(datasets, labels, TARGET_START)
    method_name = "Percentage Change"

# 应用平滑处理
def apply_smoothing(datasets, window_size=29, poly_order=3):
    """应用Savitzky-Golay平滑"""
    smoothed_datasets = []
    
    for data in datasets:
        data_smooth = data.copy()
        
        # 检查数据长度并调整窗口大小
        if len(data) < window_size:
            actual_window = len(data) if len(data) % 2 == 1 else len(data) - 1
            if actual_window < 3:
                actual_window = 3
        else:
            actual_window = window_size
        
        # 应用平滑
        data_smooth['smoothed_value'] = savgol_filter(data['Value'], actual_window, poly_order)
        data_smooth['std'] = data['Value'].rolling(window=actual_window, min_periods=1).std()
        
        smoothed_datasets.append(data_smooth)
    
    return smoothed_datasets

smoothed_datasets = apply_smoothing(processed_datasets)

# 最终验证起点统一性
print(f"\n=== 最终验证 ===")
print("平滑后的起点Value:")
final_starts = []
for data, label in zip(smoothed_datasets, labels):
    start_value = data['smoothed_value'].iloc[0]
    final_starts.append(start_value)
    print(f"{label}: {start_value:.6f}")

# 检查起点是否真正统一
start_diff = max(final_starts) - min(final_starts)
print(f"起点最大差异: {start_diff:.6f}")

if start_diff < 0.01:
    print("✅ 起点已完美统一!")
else:
    print("⚠️ 起点仍有差异，需要进一步调整")
    
    # 如果还有差异，进行最终微调
    print("\n进行最终微调...")
    target_exact = TARGET_START
    for i, (data, label) in enumerate(zip(smoothed_datasets, labels)):
        current_start = data['smoothed_value'].iloc[0]
        micro_offset = target_exact - current_start
        data['smoothed_value'] = data['smoothed_value'] + micro_offset
        print(f"{label}: 微调 {micro_offset:.6f}")

# 自定义数字格式
def custom_formatter(x, pos):
    return f'{x:,.0f}'

# 绘制最终图表
plt.figure(figsize=(15, 8))

# 绘制所有曲线
for data, label, color in zip(smoothed_datasets, labels, colors):
    plt.plot(data['Step'], data['smoothed_value'], label=label, color=color, linewidth=2.5)
    plt.fill_between(data['Step'],
                     data['smoothed_value'] - data['std'],
                     data['smoothed_value'] + data['std'],
                     color=color, alpha=0.15)

# 添加统一起点标记
plt.axhline(y=TARGET_START, color='red', linestyle=':', alpha=0.8, linewidth=2, 
           label=f'Unified Start Point ({TARGET_START})')

# 在起点位置添加标记点
for data, color in zip(smoothed_datasets, colors):
    plt.plot(data['Step'].iloc[0], data['smoothed_value'].iloc[0], 'o', 
             color=color, markersize=10, markeredgecolor='white', markeredgewidth=2, zorder=5)

# 图表美化
plt.title(f'Average Reward over Time\n({method_name} to Unified Starting Point)', 
          fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Episodes', fontsize=14)
plt.ylabel('Reward (Normalized)', fontsize=14)
plt.legend(fontsize=12, loc='best', framealpha=0.9)

# 设置坐标轴格式
plt.gca().xaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))
plt.gca().yaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))

# 添加网格
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)

# 设置坐标轴范围
all_steps = np.concatenate([data['Step'].values for data in smoothed_datasets])
all_values = np.concatenate([data['smoothed_value'].values for data in smoothed_datasets])

plt.xlim(min(all_steps) - 100, max(all_steps) + 100)
plt.ylim(min(all_values) - 200, max(all_values) + 200)

# 添加文本说明
textstr = f'All algorithms start from {TARGET_START:.0f}\nOriginal starts: {[f"{x:.0f}" for x in original_starts]}'
props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
         verticalalignment='top', bbox=props)

plt.tight_layout()
plt.savefig('奖励图_新数据统一起点.png', dpi=300, bbox_inches='tight')
print(f"\n✅ 新数据统一起点图表已保存为: 奖励图_新数据统一起点.png")
print(f"📍 所有算法现在都从 {TARGET_START} 开始")
plt.show()
