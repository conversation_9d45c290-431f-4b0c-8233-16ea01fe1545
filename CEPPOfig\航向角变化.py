import pandas as pd
import matplotlib.pyplot as plt

# ===== 修改为你的 Excel 文件路径 =====
file_path = 'C:/Users/<USER>/Desktop/实验数据/无障碍物/1.xlsx'

# ===== 读取 Excel 文件（默认读取第一个工作表）=====
df = pd.read_excel(file_path)

# ===== 提取第 5 列（假设前四列是0~3，舵角为索引4）=====
rudder_angles = df.iloc[:, 4]

# ===== 绘制图像 =====
plt.figure(figsize=(10, 4))
plt.plot(rudder_angles, label='PPO', color='orange')
plt.xlabel('Time Step')
plt.ylabel('Angle (°)')
plt.title('Heading Angle Variation Comparison')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
