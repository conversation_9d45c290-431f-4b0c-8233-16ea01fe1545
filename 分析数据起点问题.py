import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import numpy as np

# 读取Excel文件
file_paths = [
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'
]

labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']
colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']

print("=== 详细分析数据起点问题 ===\n")

datasets = []
for file_path, label in zip(file_paths, labels):
    try:
        data = pd.read_excel(file_path)
        datasets.append(data)
        
        print(f"--- {label} ---")
        print(f"数据形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
        
        # 显示前10行原始数据
        print("前10行原始数据:")
        if 'Step' in data.columns and 'Value' in data.columns:
            print(data[['Step', 'Value']].head(10).to_string(index=False))
            
            # 检查第一个数据点
            first_step = data['Step'].iloc[0]
            first_value = data['Value'].iloc[0]
            print(f"第一个数据点: Step={first_step}, Value={first_value}")
            
            # 检查是否有Step=0但Value不为0的情况
            step_zero_data = data[data['Step'] == 0]
            print(f"Step=0的数据点数量: {len(step_zero_data)}")
            if len(step_zero_data) > 0:
                print("所有Step=0的数据:")
                print(step_zero_data[['Step', 'Value']].to_string(index=False))
        
        print("\n" + "="*60 + "\n")
        
    except Exception as e:
        print(f"读取 {label} 时出错: {e}\n")

# 如果成功读取所有数据，进行平滑处理分析
if len(datasets) == 4:
    print("=== 平滑处理对起点的影响分析 ===\n")
    
    # 应用与原代码相同的处理
    window_size = 29
    poly_order = 3
    
    # 过滤数据
    filtered_datasets = []
    for data in datasets:
        filtered_data = data[data['Step'] < 10000].copy()
        filtered_datasets.append(filtered_data)
    
    print("过滤后的起点分析:")
    for data, label in zip(filtered_datasets, labels):
        if len(data) > 0:
            first_step = data['Step'].iloc[0]
            first_value = data['Value'].iloc[0]
            print(f"{label}: Step={first_step}, Value={first_value}")
    
    print(f"\n应用Savitzky-Golay平滑 (window_size={window_size}, poly_order={poly_order}):")
    
    smoothed_datasets = []
    for data, label in zip(filtered_datasets, labels):
        data_smooth = data.copy()
        
        # 检查数据长度
        if len(data) >= window_size:
            smoothed_values = savgol_filter(data['Value'], window_size, poly_order)
            data_smooth['smoothed_value'] = smoothed_values
            
            # 比较原始值和平滑值
            original_first = data['Value'].iloc[0]
            smoothed_first = smoothed_values[0]
            
            print(f"{label}:")
            print(f"  原始第一个Value: {original_first:.6f}")
            print(f"  平滑后第一个Value: {smoothed_first:.6f}")
            print(f"  差异: {smoothed_first - original_first:.6f}")
            
            # 显示前5个原始值和平滑值的对比
            print("  前5个数据点对比:")
            for i in range(min(5, len(data))):
                orig = data['Value'].iloc[i]
                smooth = smoothed_values[i]
                print(f"    点{i+1}: 原始={orig:.2f}, 平滑={smooth:.2f}, 差异={smooth-orig:.2f}")
            
        else:
            print(f"{label}: 数据长度 {len(data)} 小于窗口大小 {window_size}")
            data_smooth['smoothed_value'] = data['Value']
        
        smoothed_datasets.append(data_smooth)
        print()
    
    # 可视化对比
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i, (data, label, color) in enumerate(zip(smoothed_datasets, labels, colors)):
        ax = axes[i]
        
        # 只显示前50个数据点进行对比
        plot_data = data.head(50)
        
        # 绘制原始数据
        ax.plot(plot_data['Step'], plot_data['Value'], 'o-', 
                color=color, alpha=0.5, markersize=3, linewidth=1, label='原始数据')
        
        # 绘制平滑数据
        if 'smoothed_value' in plot_data.columns:
            ax.plot(plot_data['Step'], plot_data['smoothed_value'], '-', 
                    color=color, linewidth=2, label='平滑数据')
        
        ax.set_title(f'{label}\n前50个数据点对比')
        ax.set_xlabel('Step')
        ax.set_ylabel('Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 标记起点
        if len(plot_data) > 0:
            first_step = plot_data['Step'].iloc[0]
            first_orig = plot_data['Value'].iloc[0]
            ax.plot(first_step, first_orig, 'ro', markersize=8, label=f'起点: {first_orig:.1f}')
            
            if 'smoothed_value' in plot_data.columns:
                first_smooth = plot_data['smoothed_value'].iloc[0]
                ax.plot(first_step, first_smooth, 'bs', markersize=8, label=f'平滑起点: {first_smooth:.1f}')
    
    plt.tight_layout()
    plt.savefig('数据起点问题分析.png', dpi=300, bbox_inches='tight')
    print("分析图表已保存为: 数据起点问题分析.png")
    plt.show()
    
    print("\n=== 问题总结 ===")
    print("1. 检查您的Excel数据第一行是否真的是Step=0, Value=0")
    print("2. Savitzky-Golay平滑会改变起点值，因为它考虑周围数据点")
    print("3. 如果您希望图从0开始，需要在平滑后手动调整起点")
    print("4. 或者可以在数据开头添加几个Value=0的点来稳定平滑效果")
