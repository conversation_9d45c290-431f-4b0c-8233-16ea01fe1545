import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取数据文件
file_paths = [
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'
]

labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']

print("=== 数据分析报告 ===\n")

# 分析每个数据文件
for i, (file_path, label) in enumerate(zip(file_paths, labels)):
    try:
        data = pd.read_excel(file_path)
        print(f"--- {label} ---")
        print(f"数据形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
        
        if 'Step' in data.columns:
            print(f"Step范围: {data['Step'].min()} - {data['Step'].max()}")
            print(f"Step起始值: {data['Step'].iloc[0]}")
            print(f"Step间隔: {data['Step'].iloc[1] - data['Step'].iloc[0] if len(data) > 1 else 'N/A'}")
        
        if 'Value' in data.columns:
            print(f"Value范围: {data['Value'].min():.2f} - {data['Value'].max():.2f}")
            print(f"Value起始值: {data['Value'].iloc[0]:.2f}")
            print(f"Value前5个值: {data['Value'].head().tolist()}")
        
        print(f"前3行数据:")
        print(data.head(3))
        print("\n" + "="*50 + "\n")
        
    except Exception as e:
        print(f"读取 {label} 时出错: {e}\n")

# 如果所有文件都能读取，进行对比分析
try:
    datasets = []
    for file_path in file_paths:
        data = pd.read_excel(file_path)
        datasets.append(data)
    
    print("=== 起点对比分析 ===")
    
    # 检查Step起点
    step_starts = [data['Step'].iloc[0] for data in datasets]
    print(f"各算法Step起点: {dict(zip(labels, step_starts))}")
    
    if len(set(step_starts)) > 1:
        print("⚠️  发现问题: Step起点不一致!")
        print("建议解决方案:")
        print("1. 统一所有数据的Step起点为0")
        print("2. 或者找到最大的起点值，让所有数据从该点开始")
    
    # 检查Value起点
    value_starts = [data['Value'].iloc[0] for data in datasets]
    print(f"各算法Value起点: {dict(zip(labels, value_starts))}")
    
    if max(value_starts) - min(value_starts) > 100:  # 如果差异超过100
        print("⚠️  发现问题: Value起点差异较大!")
        print("这可能是由于:")
        print("- 不同算法的初始化策略不同")
        print("- 数据记录的起始时间点不同")
        print("- 训练环境的初始状态不同")
    
    # 检查数据长度
    lengths = [len(data) for data in datasets]
    print(f"各算法数据长度: {dict(zip(labels, lengths))}")
    
    if len(set(lengths)) > 1:
        print("⚠️  发现问题: 数据长度不一致!")
    
    # 可视化起点问题
    plt.figure(figsize=(15, 10))
    
    # 子图1: 原始数据对比
    plt.subplot(2, 2, 1)
    colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']
    for i, (data, label, color) in enumerate(zip(datasets, labels, colors)):
        plt.plot(data['Step'][:100], data['Value'][:100], label=label, color=color, marker='o', markersize=3)
    plt.title('前100个数据点对比')
    plt.xlabel('Step')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    # 子图2: 起点放大
    plt.subplot(2, 2, 2)
    for i, (data, label, color) in enumerate(zip(datasets, labels, colors)):
        plt.plot(data['Step'][:20], data['Value'][:20], label=label, color=color, marker='o', markersize=5)
    plt.title('前20个数据点放大')
    plt.xlabel('Step')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    # 子图3: Step起点对比
    plt.subplot(2, 2, 3)
    plt.bar(labels, step_starts, color=colors)
    plt.title('Step起点对比')
    plt.ylabel('Step起始值')
    plt.xticks(rotation=45)
    
    # 子图4: Value起点对比
    plt.subplot(2, 2, 4)
    plt.bar(labels, value_starts, color=colors)
    plt.title('Value起点对比')
    plt.ylabel('Value起始值')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig('reward_data_analysis.png', dpi=300, bbox_inches='tight')
    print("\n分析图表已保存为: reward_data_analysis.png")
    plt.show()
    
except Exception as e:
    print(f"对比分析时出错: {e}")
