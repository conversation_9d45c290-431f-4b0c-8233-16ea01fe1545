{"print亮黄": "PrintBrightYellow", "print亮绿": "PrintBrightGreen", "print亮红": "PrintBrightRed", "print红": "PrintRed", "print绿": "PrintGreen", "print黄": "PrintYellow", "print蓝": "PrintBlue", "print紫": "PrintPurple", "print靛": "PrintIndigo", "print亮蓝": "PrintBrightBlue", "print亮紫": "PrintBrightPurple", "print亮靛": "PrintBrightIndigo", "读文章写摘要": "ReadArticleWriteSummary", "批量生成函数注释": "BatchGenerateFunctionComments", "生成函数注释": "GenerateFunctionComments", "解析项目本身": "ParseProjectItself", "解析项目源代码": "ParseProjectSourceCode", "解析一个Python项目": "ParsePythonProject", "解析一个C项目的头文件": "ParseCProjectHeaderFile", "解析一个C项目": "ParseACProject", "解析一个Golang项目": "ParseAGolangProject", "解析一个Rust项目": "ParseARustProject", "解析一个Java项目": "ParseAJavaProject", "解析一个前端项目": "ParseAFrontendProject", "高阶功能模板函数": "AdvancedFeatureTemplateFunction", "高级功能函数模板": "AdvancedFunctionTemplate", "全项目切换英文": "SwitchProjectToEnglish", "代码重写为全英文_多线程": "RewriteCodeToEnglish_Multithreading", "Latex英文润色": "LatexEnglishProofreading", "Latex全文润色": "LatexFullTextProofreading", "同时问询": "SimultaneousInquiry", "询问多个大语言模型": "InquireMultipleLargeLanguageModels", "解析一个Lua项目": "ParseALuaProject", "解析一个CSharp项目": "ParseACSharpProject", "总结word文档": "SummarizeWordDocument", "解析ipynb文件": "ParseIpynbFile", "解析JupyterNotebook": "ParseJupyterNotebook", "Conversation_To_File": "ConversationHistoryArchive", "载入Conversation_To_File": "LoadConversationHistoryArchive", "删除所有本地对话历史记录": "DeleteAllLocalChatHistory", "Markdown英译中": "MarkdownTranslateFromEngToChi", "Markdown_Translate": "BatchTranslateMarkdown", "批量总结PDF文档": "BatchSummarizePDFDocuments", "批量总结PDF文档pdfminer": "BatchSummarizePDFDocumentsUsingPDFMiner", "批量翻译PDF文档": "BatchTranslatePDFDocuments", "PDF_Translate": "BatchTranslatePDFDocumentsUsingMultiThreading", "谷歌检索小助手": "GoogleSearchAssistant", "理解PDF文档内容标准文件输入": "StandardFileInputForUnderstandingPDFDocumentContent", "理解PDF文档内容": "UnderstandingPDFDocumentContent", "Latex中文润色": "ChineseProofreadingInLatex", "Latex中译英": "ChineseToEnglishTranslationInLatex", "Latex全文翻译": "FullTextTranslationInLatex", "Latex英译中": "EnglishToChineseTranslationInLatex", "Markdown中译英": "TranslateFromChiToEngInMarkdown", "下载arxiv论文并翻译摘要": "DownloadArxivPapersAndTranslateAbstract", "下载arxiv论文翻译摘要": "DownloadArxivPapersAndTranslateAbstract", "连接网络回答问题": "ConnectToInternetAndAnswerQuestions", "联网的ChatGPT": "ChatGPTConnectedToInternet", "解析任意code项目": "ParseAnyCodeProject", "同时问询_指定模型": "InquireSpecifiedModelAtTheSameTime", "图片生成": "GenerateImage", "test_解析ipynb文件": "test_ParseIpynbFile", "把字符太少的块清除为回车": "RemoveBlocksWithTooFewCharactersToNewline", "清理多余的空行": "CleanUpExtraBlankLines", "合并小写开头的段落块": "MergeParagraphBlocksStartingWithLowerCase", "多文件润色": "PolishMultipleFiles", "多文件翻译": "TranslateMultipleFiles", "解析docx": "ParseDocx", "解析PDF": "ParsePDF", "解析Paper": "ParsePaper", "ipynb解释": "InterpretIpynb", "解析源代码新": "ParseSourceCodeNew", "填写格式是": "入力フォーマットは", "并在新模块中重新加载函数": "新しいモジュールで関数を再読み込みする", "如果要使用MOSS": "MOSSを使用する場合", "翻译成地道的中文": "自然な中国語に翻訳する", "请对下面的程序文件做一个概述": "以下のプログラムファイルについて概要を説明してください", "用tex格式": "TeX形式で", "浮点数": "浮動小数点数", "第三部分": "第3部分", "这个函数运行在子进程": "この関数はサブプロセスで実行されます", "自动解压": "自動解凍", "按Enter提交": "Enterを押して提出する", "如果超过期限没有喂狗": "期限を過ぎてもフィードしない場合", "正在开始汇总": "集計を開始しています", "安装jittorllms依赖后将完全破坏现有的pytorch环境": "jittorllmsの依存関係をインストールすると、既存のpytorch環境が完全に破壊されます", "尝试加载": "読み込みを試みる", "* 此函数未来将被弃用": "* この関数は将来的に廃止されます", "newbing回复的片段": "newbingの返信フラグメント", "新版本可用": "新しいバージョンが利用可能です", "函数插件区": "関数プラグインエリア", "jittorllms消耗大量的内存": "jittorllmsは大量のメモリを消費します", "替换跨行的连词": "複数行の接続詞を置換する", "Markdown/Readme英译中": "Markdown/Readmeの英訳中", "如果需要使用newbing": "newbingを使用する必要がある場合", "对整个Markdown项目进行翻译": "Markdownプロジェクト全体を翻訳する", "比正文字体小": "本文より小さいフォントサイズ", "请对下面的文章片段做概述": "以下の記事の断片について概要を説明してください", "正在获取文献名！": "文献名を取得しています！", "展现在报告中的输入": "レポートに表示される入力", "则删除报错信息": "エラーメッセージを削除する", "第3步": "ステップ3", "尚未充分测试的函数插件": "十分にテストされていない関数プラグイン", "You exceeded your current quota. OpenAI以账户额度不足为由": "現在のクォータを超過しました。OpenAIはアカウントのクォータ不足を理由にしています", "下载完成": "ダウンロードが完了しました", "正常结束": "正常に終了しました", "第1步": "ステップ1", "必要时": "必要に応じて", "留空即可": "空白のままにしておくことができます", "文件名是": "ファイル名は", "双层列表": "二重リスト", "上下文管理器是一种Python对象": "コンテキストマネージャはPythonオブジェクトの一種です", "**输出参数说明**": "**出力パラメータの説明**", "history至少释放二分之一": "historyは少なくとも半分解放する必要があります", "拒绝服务": "サービスを拒否する", "默认按钮颜色是 secondary": "デフォルトのボタンの色はsecondaryです", "加了^代表不匹配": "^を追加すると、一致しないことを意味します", "读取时首先看是否存在私密的config_private配置文件": "読み取り時に、まずconfig_private構成ファイルが存在するかどうかを確認します", "如果这里抛出异常": "ここで例外が発生した場合", "缺少api_key": "api_keyが不足しています", "而cl**h 的默认本地协议是http": "cl ** hのデフォルトのローカルプロトコルはhttpです", "尝试计算比例": "比率を計算しようとする", "你是一个程序架构分析师": "あなたはプログラムアーキテクチャアナリストです", "jittorllms响应异常": "jittorllms応答異常", "开始问问题": "質問を始める", "的模板": "のテンプレート", "加一个live2d装饰": "live2dの装飾を追加する", "经过充分测试": "十分にテストされた後", "gradio版本较旧": "Gradioのバージョンが古いです", "配置信息如下": "以下は構成情報です", "刷新用户界面": "ユーザーインターフェースを更新する", "翻译": "翻訳", "读取配置": "構成を読み込む", "第二种情况": "2番目の場合", "接下来": "次に", "合并小写字母开头的段落块并替换为空格": "小文字で始まる段落ブロックを結合して空白に置き換える", "质能方程是描述质量与能量之间的当量关系的方程": "質量とエネルギーの間の等価関係を記述する質量エネルギー方程式", "匹配^数字^": "^数字^に一致する", "提高语法、清晰度和整体可读性": "文法、明確さ、全体的な読みやすさを向上させる", "对最相关的两个搜索结果进行总结": "最も関連性の高い2つの検索結果をまとめる", "另外您可以随时在history子文件夹下找回旧版的程序": "また、いつでもhistoryサブフォルダーで古いバージョンのプログラムを取得できます", "将每个换行符替换为两个换行符": "各改行文字を2つの改行文字に置き換える", "调用NewBing时": "NewBingを呼び出すとき", "接下来请你逐文件分析下面的工程": "次に、以下のプロジェクトをファイルごとに分析してください", "不可高于3": "3を超えることはできません", "本项目现已支持OpenAI和API2D的api-key": "このプロジェクトは現在、OpenAIおよびAPI2DのAPIキーをサポートしています", "llm_kwargs参数": "llm_kwargsパラメータ", "切割PDF": "PDFを切り分ける", "随便切一下敷衍吧": "適当に切ってください", "按照章节切割PDF": "章ごとにPDFを切り分ける", "聊天显示框的句柄": "チャット表示ボックスのハンドル", "已删除": "削除されました", "如果没有指定文件名": "ファイル名が指定されていない場合", "Tiktoken未知错误": "Tiktokenの未知のエラー", "你的回答必须简单明了": "回答は簡潔で明確でなければなりません", "\\n 翻译": "\\n翻訳", "2. 长效解决方案": "長期的な解決策", "上下文": "文脈", "图像中转网址": "画像の中継ウェブサイト", "感叹号": "感嘆符", "第 4 步": "4番目のステップ", "为了安全而隐藏绝对地址": "安全のために絶対アドレスを隠す", "获取成功": "取得成功", "综合": "総合", "在执行过程中遭遇问题": "実行中に問題が発生しました", "输入参数 Args": "入力パラメータArgs", "在项目根目录运行这两个指令": "プロジェクトのルートディレクトリでこれら2つのコマンドを実行する", "文件内容是": "ファイルの内容は", "css等": "CSSなど", "发送请求到OpenAI后": "OpenAIにリクエストを送信した後", "来保留函数的元信息": "関数のメタ情報を保持するために", "第3次尝试": "3回目の試み", "我们": "私たちは", "注意无论是inputs还是history": "inputsまたはhistoryである場合でも注意してください", "本地路径": "ローカルパス", "1. 对原始文本进行归一化处理": "1.元のテキストを正規化する", "这个文件用于函数插件的单元测试": "このファイルは関数プラグインのユニットテストに使用されます", "用于基础的对话功能": "基本的な対話機能に使用されます", "代理设置": "プロキシ設定", "在此处替换您要搜索的关键词": "ここで検索するキーワードを置き換えてください", "请求GPT模型同时维持用户界面活跃": "GPTモデルにリクエストを送信しながら、ユーザーインターフェイスを活性化します", "3. 根据 heuristic 规则判断换行符是否是段落分隔": "3.ヒューリスティックルールに従って、改行が段落の区切りかどうかを判断する", "temperature是LLM的内部调优参数": "temperatureはLLMの内部調整パラメータです", "发送到chatgpt进行分析": "chatgptに送信して分析する", "在config.py中配置": "config.pyに設定する", "第 1 步": "ステップ1", "定义注释的正则表达式": "コメントの正規表現を定義する", "OpenAI绑了信用卡的用户可以填 16 或者更高": "OpenAIにクレジットカードをバインドしているユーザーは、16以上を入力できます", "模仿ChatPDF": "ChatPDFを模倣する", "以_array结尾的输入变量都是列表": "_arrayで終わる入力変数はすべてリストです", "终止按钮的回调函数注册": "停止ボタンのコールバック関数の登録", "意外Json结构": "予期しないJson構造", "需要安装pip install py7zr来解压7z文件": "7zファイルを解凍するには、pip install py7zrをインストールする必要があります", "将Unsplash API中的PUT_YOUR_QUERY_HERE替换成描述该事件的一个最重要的单词": "Unsplash APIのPUT_YOUR_QUERY_HEREを、そのイベントを最もよく表す単語に置き換えます", "预处理": "前処理", "状态": "ステータス", "知乎": "知乎", "聊天历史": "チャット履歴", "请从给定的若干条搜索结果中抽取信息": "指定された複数の検索結果から情報を抽出してください", "通过裁剪来缩短历史记录的长度": "履歴の長さを短くするためにトリミングを使用する", "函数插件作者": "関数プラグインの作者", "这个中文的句号是故意的": "この中国語の句点は意図的です", "双换行": "二重改行", "用了很多trick": "多くのトリックを使用しました", "如.md": ".mdのように", "屏蔽掉 chatglm的多线程": "chatglmのマルチスレッドをブロックする", "但显示Token不足": "ただし、トークンが不足していると表示されます", "对文本进行归一化处理": "テキストを正規化する", "把结果写入文件": "結果をファイルに書き込む", "如果没找到任何文件": "ファイルが見つからなかった場合", "请确认是否满足您的需要": "必要条件を満たしているかどうかを確認してください", "您提供的api-key不满足要求": "提供されたAPIキーが要件を満たしていません", "MOSS消耗大量的内存": "MOSSは大量のメモリを消費します", "文本过长将进行截断": "テキストが長すぎる場合は切り捨てられます", "橙色": "オレンジ色", "失败时的重试次数": "失敗時の再試行回数", "+ 已经汇总的文件组": "すでにまとめられたファイルグループ", "相关功能不稳定": "関連機能は不安定です", "将要匹配的模式": "マッチングするパターン", "第4步": "ステップ4", "调用时": "呼び出し時", "问询记录": "問い合わせ記録", "不能正常加载MOSS的参数！": "MOSSのパラメータを正常にロードできません！", "接管gradio默认的markdown处理方式": "gradioのデフォルトのmarkdown処理方法を接管する", "加载tokenizer完毕": "tokenizerの読み込みが完了しました", "请用markdown格式输出": "markdown形式で出力してください", "PDF文件也已经下载": "PDFファイルもダウンロードされました", "读取Latex文件": "Latexファイルを読み込む", "找不到任何.tex或.pdf文件": ".texまたは.pdfファイルが見つかりません", "端口": "ポート", "此外": "さらに", "使用yield from语句返回重新加载过的函数": "yield fromステートメントを使用して再読み込みされた関数を返す", "函数插件贡献者": "関数プラグインの貢献者", "绿色": "緑色", "酸橙色": "ライムグリーン", "找不到本地项目或无权访问": "ローカルプロジェクトが見つからないか、アクセス権がありません", "此函数逐渐地搜索最长的条目进行剪辑": "この関数は徐々に最長のエントリを検索して編集します", "注意这里的历史记录被替代了": "ここでの履歴は置き換えられました", "但大部分场合下并不需要修改": "ただし、ほとんどの場合、変更は必要ありません", "这个内部函数可以将函数的原始定义更新为最新版本": "この内部関数は、関数の元の定義を最新バージョンに更新できます", "输出了前面的": "前のものを出力し、1つの文字列に結合します", "并合并为一个字符串": "前のものを出力し、1つの文字列に結合します", "出现的所有文章": "表示されるすべての記事", "pip包依赖安装出现问题": "pipパッケージの依存関係のインストールに問題が発生しました", "用于重组输入参数": "入力パラメーターを再構成するために使用されます", "格式须是": "フォーマットは次のようにする必要があります", "请注意proxies选项的格式": "proxiesオプションの形式に注意してください", "api_key已导入": "api_keyがインポートされました", "新版配置": "新しいバージョンの設定", "暂时没有用武之地": "現時点では使用されていません", "返回文本内容": "テキストコンテンツを返します", "从而避免解析压缩文件": "圧縮ファイルの解析を回避するため", "环境变量可以是": "環境変数は次のようにすることができます", "接下来两句话只显示在界面上": "次の2つの文は、画面にのみ表示されます", "解析的结果如下": "解析結果は以下のとおりです", "若上传压缩文件": "圧縮ファイルをアップロードする場合", "找不到任何html文件": "htmlファイルが見つかりません", "环境变量": "環境変数", "备选输入区": "代替入力エリア", "如果文章被切分了": "記事が分割された場合", "异常原因": "異常の原因", "生成带有段落标签的HTML代码": "段落タグを持つHTMLコードを生成する", "按钮颜色": "ボタンの色", "请只提供文本的更正版本": "テキストの修正バージョンのみを提供してください", "输入": "入力", "插件参数区": "プラグインパラメータエリア", "玫瑰色": "ローズ色", "根据以上分析": "上記の分析に基づいて", "解析整个Go项目": "Goプロジェクト全体を解析する", "解析整个Rust项目": "Rustプロジェクト全体を解析する", "新功能": "新機能", "避免代理网络产生意外污染": "プロキシネットワークによる予期しない汚染を回避する", "检测到": "検出された", "借助此参数": "このパラメータを利用する", "重置": "リセット", "优先级2. 获取config_private中的配置": "優先度2. config_privateから設定を取得する", "具备以下功能": "以下の機能を備えています", "的耐心": "の忍耐力", "将输出代码片段的“后面的": "コードスニペットの後ろに出力する", "等待重试": "再試行を待つ", "覆盖和重启": "上書きして再起動する", "ChatGPT 学术优化": "ChatGPT学術最適化", "后面两句是": "後の2文は", "检查代理服务器是否可用": "プロキシサーバーが利用可能かどうかを確認する", "存在一行极长的文本！": "1行の非常に長いテキストが存在します！", "减少重复": "重複を減らす", "暗色主题": "ダークテーマ", "提取出以下内容": "以下の内容を抽出する", "先在input输入编号": "まずinputに番号を入力してください", "当输入部分的token占比小于限制的3/4时": "入力部分のトークンの割合が制限の3/4未満の場合", "检测输入参数": "入力パラメータを検出する", "api-key不满足要求": "api-keyが要件を満たしていない", "刷新界面": "画面を更新する", "重试的次数限制": "再試行回数の制限", "输入路径或上传压缩包": "パスを入力するか、圧縮ファイルをアップロードする", "如果某个子任务出错": "サブタスクのいずれかがエラーになった場合", "已经全部完成": "すべて完了しました", "并对文件中的所有函数生成注释": "すべての関数にコメントを生成する", "如果选择自动处理": "自動処理を選択した場合", "缺少的依赖": "不足している依存関係", "紫色": "紫色", "唤起高级参数输入区": "高度なパラメータ入力エリアを呼び出す", "则换行符更有可能表示段落分隔": "したがって、改行記号は段落の区切りを表す可能性がより高いです", "；4、引用数量": "；4、引用数量", "中转网址预览": "中継ウェブサイトのプレビュー", "批量总结Word文档": "Word文書を一括で要約する", "建议低于1": "1未満をお勧めします", "并且将结合上下文内容": "そして文脈内容を結合します", "整合所有信息": "すべての情報を統合する", "解析整个Lua项目": "Luaプロジェクト全体を解析する", "它的作用是……额……就是不起作用": "その役割は……ああ……機能しないことです", "列表长度为子任务的数量": "リストの長さはサブタスクの数です", "为实现更多强大的功能做基础": "より強力な機能を実現するための基盤となる", "请从数据中提取信息": "データから情報を抽出してください", "至少一个线程任务Token溢出而失败": "少なくとも1つのスレッドタスクトークンがオーバーフローして失敗します", "是否自动处理token溢出的情况": "トークンのオーバーフローを自動的に処理するかどうか", "本地LLM模型如ChatGLM的执行方式 CPU/GPU": "ローカルLLMモデルの実行方法、例えばChatGLM CPU/GPU", "等待中": "待機中", "任务函数": "タスク関数", "等文本特殊符号转换为其基本形式来对文本进行归一化处理": "テキストの特殊記号を基本形式に変換してテキストを正規化する", "集合文件": "集合ファイル", "替换其他特殊字符": "他の特殊文字を置換する", "选择LLM模型": "LLMモデルを選択する", "超过512个": "512を超える", "装载请求内容": "リクエストコンテンツをロードする", "根据前后相邻字符的特点": "前後の文字の特徴に基づく", "GPT模型返回的回复字符串": "GPTモデルからの返信文字列", "将对话记录history以Markdown格式写入文件中": "対話履歴をMarkdown形式でファイルに書き込む", "无法连接到该网页": "このウェブページに接続できません", "**输入参数说明**": "**入力パラメータの説明**", "设置用户名和密码": "ユーザー名とパスワードを設定する", "GPT参数": "GPTパラメータ", "请用代码块输出代码": "コードブロックでコードを出力してください", "保存当前的对话": "現在の対話を保存する", "在这里输入分辨率": "解像度をここに入力してください", "不能正常加载jittorllms的参数！": "jittorllmsのパラメータを正常にロードできません！", "如果包含数学公式": "数式が含まれている場合", "子线程任务": "サブスレッドタスク", "；5、中文摘要翻译": "；5、中国語要約翻訳", "截断时的颗粒度": "切り捨て時の粒度", "作为一名中文学术论文写作改进助理": "中国語学術論文の執筆改善アシスタントとして", "解析网页内容": "ウェブページの内容を解析する", "作为切分点": "分割点として", "将长文本分离开来": "長いテキストを分離する", "总结文章": "記事をまとめる", "左右布局": "左右レイアウト", "用户取消了程序": "ユーザーがプログラムをキャンセルしました", "多线程函数插件中": "マルチスレッド関数プラグインで", "不能识别的URL！": "認識できないURL！", "逐个文件分析已完成": "1つずつファイルを分析しました", "感谢热情的": "熱心な感謝", "是本次输出": "今回の出力です", "协议": "プロトコル", "例如需要翻译的一段话": "翻訳が必要な例文", "本地文件地址": "ローカルファイルアドレス", "更好的UI视觉效果": "より良いUI視覚効果", "窗口布局": "ウィンドウレイアウト", "测试功能": "テスト機能", "前者API2D的": "前者API2Dの", "请缩减输入文件的数量": "入力ファイルの数を減らしてください", "随便显示点什么防止卡顿的感觉": "何か表示してカクつきを防止する", "删除所有历史对话文件": "すべての履歴対話ファイルを削除する", "是否在输入过长时": "入力が長すぎる場合は", "只保留文件名节省token": "ファイル名のみを保持してトークンを節約する", "插件模型的参数": "プラグインモデルのパラメータ", "若再次失败则更可能是因为输入过长.": "再度失敗した場合、入力が長すぎる可能性が高いです。", "或历史数据过长. 历史缓存数据已部分释放": "または履歴データが長すぎます。履歴キャッシュデータは一部解放されました", "虽然不同的代理软件界面不一样": "異なるプロキシソフトウェアのインターフェースは異なりますが", "英译中": "英語から中国語への翻訳", "第4次尝试": "4回目の試み", "批": "バッチ", "方便调试和定位问题": "デバッグと問題の特定を容易にする", "IP查询频率受限": "IPクエリ頻度が制限されています", "则不解析notebook中的Markdown块": "したがって、ノートブックのMarkdownブロックを解析しない", "英语关键词": "英語のキーワード", "热更新prompt": "プロンプトのホット更新", "保存当前对话": "現在の対話を保存する", "我们用最暴力的方法切割": "最も暴力的な方法で切り分けます", "Index 0 文本": "インデックス0テキスト", "最大线程数": "最大スレッド数", "然后用for+append循环重新赋值": "for+appendループを使用して値を再割り当てする", "获取文章meta信息": "記事のメタ情報を取得する", "Pay-as-you-go users的限制是每分钟3500次": "Pay-as-you-goユーザーの制限は1分間に3500回です", "请注意": "注意してください", "的转化": "の変換", "解析Jupyter Notebook文件": "<PERSON><PERSON><PERSON> Notebookファイルの解析", "等待多久判定为超时": "タイムアウトとして判定するまでの待機時間", "自动缩减文本": "テキストを自動的に縮小する", "返回当前系统中可用的未使用端口": "現在のシステムで使用可能な未使用のポートを返す", "历史对话输入": "過去の対話入力", "其他错误": "その他のエラー", "将错误显示出来": "エラーを表示する", "请分析此页面中出现的所有文章": "このページに表示されるすべての記事を分析してください", "将Markdown格式的文本转换为HTML格式": "Markdown形式のテキストをHTML形式に変換する", "没有 sys_prompt 接口": "sys_promptインターフェースがありません", "您可以将任意一个文件路径粘贴到输入区": "任意のファイルパスを入力エリアに貼り付けることができます", "全部文件解析完成": "すべてのファイルの解析が完了しました", "将匹配到的数字作为替换值": "一致した数字を置換値として使用する", "单行 + 字体大": "1行+フォント大", "备份和下载": "バックアップとダウンロード", "用一张Markdown表格简要描述以下文件的功能": "以下のファイルの機能を簡単にMarkdownテーブルで説明してください", "问题": "問題", "请将此部分润色以满足学术标准": "この部分を学術基準に合わせて磨き上げてください", "你是一位专业的中文学术论文作家": "あなたは専門の中国語学術論文作家です", "对话历史文件损坏！": "対話履歴ファイルが破損しています！", "重新URL重新定向": "URLを再度リダイレクトする", "输入清除键": "入力クリアキー", "因此把prompt加入 history": "したがって、履歴にpromptを追加します", "以上文件将被作为输入参数": "上記のファイルは入力パラメータとして使用されます", "的长度必须小于 2500 个 Token": "長さは2500トークン以下でなければなりません", "现在": "今", "不需要再次转化": "再変換する必要はありません", "注意文章中的每一句话都要翻译": "記事の各文は翻訳する必要があります", "整理报告的格式": "レポートのフォーマットを整理する", "请先从插件列表中选择": "まず、プラグインリストから選択してください", "带token约简功能": "トークン約束機能を備えた", "请在config文件中修改API密钥之后再运行": "APIキーを変更した後にconfigファイルで実行してください", "下载编号": "ダウンロード番号", "是否丢弃掉 不是正文的内容": "本文でない内容を破棄するかどうか", "以确保一些资源在代码块执行期间得到正确的初始化和清理": "いくつかのリソースがコードブロックの実行中に正しく初期化およびクリーンアップされるようにするため", "第一步": "ステップ1", "并将输出部分的Markdown和数学公式转换为HTML格式": "出力部分のMarkdownと数式をHTML形式に変換する", "当代码输出半截的时候": "コードが半分出力されたとき", "该文件中主要包含2个函数": "このファイルには主に2つの関数が含まれています", "提取所有块元的文本信息": "すべてのブロック要素のテキスト情報を抽出する", "成功读取环境变量": "環境変数の読み取りに成功しました", "更新完成": "更新が完了しました", "第 2 步": "ステップ2", "是否重置": "リセットしますか", "判定为数据流的结束": "データフローの終了と判断されます", "和 __exit__": "と __exit__", "将英文句号": "英文句点を", "开始接收jittorllms的回复": "jittorllmsの返信を受け取り始める", "放到每个子线程中分别执行": "それぞれのサブスレッドに配置して実行する", "作为一个标识而存在": "識別子として存在する", "你提供了错误的API_KEY": "APIキーが間違っています", "选择放弃": "キャンセルする", "请稍等": "お待ちください", "实时在UI上反馈远程数据流": "リアルタイムでUIにリモートデータストリームをフィードバックする", "用于负责跨越线程传递已经输出的部分": "スレッドを越えて出力された部分を転送する責任がある", "例如\\section": "\\セクションのように", "打印traceback": "トレースバックを印刷する", "可能需要分组处理": "グループ化処理が必要な場合があります", "应急食品是“原神”游戏中的角色派蒙的外号": "緊急食品は、「原神」ゲームのキャラクターパイモンのニックネームです", "表示函数是否成功执行": "関数が正常に実行されたかどうかを示す", "一般原样传递下去就行": "通常はそのまま渡すだけでよい", "琥珀色": "琥珀色", "jittorllms 没有 sys_prompt 接口": "jittorllmsにはsys_promptインターフェースがありません", "清除": "クリア", "小于正文的": "本文より小さい", "不懂就填localhost或者127.0.0.1肯定错不了": "わからない場合は、localhostまたは127.0.0.1を入力してください。間違いなく失敗します", "用于与with语句一起使用": "with文と一緒に使用する", "方便实现复杂的功能逻辑": "複雑な機能ロジックを実現するのに便利", "必要时再进行切割": "必要に応じて再分割する", "已失败": "失敗しました", "不具备多线程能力的函数": "マルチスレッド機能を持たない関数", "找不到任何java文件": "Javaファイルが見つかりません", "在代理软件的设置里找": "プロキシソフトウェアの設定で検索する", "装饰器函数": "デコレータ関数", "不要用代码块": "コードブロックを使用しないでください", "输入时用逗号隔开": "入力時にカンマで区切ってください", "时": "時", "找图片": "画像を検索する", "把本项目源代码切换成全英文": "このプロジェクトのソースコードをすべて英語に切り替える", "Github更新地址": "Githubの更新アドレス", "警告！API_URL配置选项将被弃用": "警告！API_URL構成オプションは廃止されます", "一、论文概况": "1.論文概要", "使用线程池": "スレッドプールを使用する", "然后请使用Markdown格式封装": "次に、Markdown形式でパッケージ化してください", "当 输入部分的token占比 小于 全文的一半时": "入力部分のトークンの割合が全体の半分以下の場合", "更新函数代码": "関数コードを更新する", "也许会导致低配计算机卡死 ……": "低スペックのコンピューターがクラッシュする可能性があります......", "sk-此处填API密钥": "sk-ここにAPIキーを入力してください", "用于实现Python函数插件的热更新": "Python関数プラグインのホット更新を実現するために使用されます", "缺一不可": "欠かせない", "回滚代码到原始的浏览器打开函数": "コードを元のブラウザ開く関数にロールバックする", "先切换模型到openai或api2d": "まず、モデルをopenaiまたはapi2dに切り替えます", "翻译为中文": "日本語に翻訳する", "收到": "受信", "需要配合修改main.py才能生效!": "有効にするには、main.pyを変更する必要があります！", "但本地存储了以下历史文件": "ただし、次の履歴ファイルがローカルに保存されています", "一些普通功能模块": "いくつかの一般的な機能モジュール", "把gradio的运行地址更改到指定的二次路径上": "Gradioの実行アドレスを指定された2次パスに変更する", "第三组插件": "第3グループのプラグイン", "避免不小心传github被别人看到": "誤ってGithubにアップロードして他の人に見られるのを避ける", "这里其实不需要join了": "ここではjoinする必要はありません", "改为True应用代理": "Trueに変更してプロキシを適用する", "粉红色": "ピンク色", "进行学术解答": "学術的な回答を行う", "用英文逗号分割": "英語のコンマで区切る", "文件保存到本地": "ローカルにファイルを保存する", "将markdown转化为好看的html": "Markdownを美しいHTMLに変換する", "灵活而简洁": "柔軟で簡潔", "当前软件运行的端口号": "現在のソフトウェアの実行ポート番号", "其他的排队等待": "その他の待ち行列", "更新失败": "更新に失敗しました", "优先级1. 获取环境变量作为配置": "優先度1. 環境変数を設定として取得する", "Y+回车=确认": "Y+Enter=確認", "石板色": "スレート色", "文件读取完成": "ファイルの読み込みが完了しました", "加载失败!": "読み込みに失敗しました！", "已经被转化过": "すでに変換されています", "提取文本块主字体": "テキストブロックの主フォントを抽出する", "多线程": "マルチスレッド", "读取pdf文件并清理其中的文本内容": "PDFファイルを読み取り、テキスト内容をクリーンアップする", "修正值": "修正値", "抽取可用的api-key": "利用可能なAPIキーを抽出する", "替换操作": "置換操作", "尚未完成全部响应": "すべての応答が完了していません", "不受git管控": "Gitの管理外", "10个文件为一组": "10ファイルを1グループとする", "生成图像": "画像を生成する", "html格式": "HTML形式", "该文件中主要包含三个函数": "このファイルには主に3つの関数が含まれています", "质能方程式": "質量エネルギー方程式", "高级函数插件": "高度な関数プラグイン", "随变按钮的回调函数注册": "可変ボタンのコールバック関数の登録", "份搜索结果": "検索結果", "如果浏览器没有自动打开": "ブラウザが自動的に開かない場合", "仅支持Win平台": "Winプラットフォームのみサポート", "模块预热": "モジュールのプレヒート", "请解释以下代码": "以下のコードを説明してください", "具备完备的交互功能": "完全なインタラクティブ機能を備えています", "则给出安装建议": "インストールの提案を行います", "既可以写": "書くことができます", "已成功": "成功しました", "需要用此选项防止高频地请求openai导致错误": "このオプションを使用して、openaiへの高頻度のリクエストを防止し、エラーを引き起こす必要があります", "则终止": "停止する", "Call MOSS fail 不能正常加载MOSS的参数": "MOSSのパラメータを正常にロードできないため、Call MOSS fail", "依次访问网页": "ウェブページに順次アクセスする", "暂时先这样顶一下": "一時的にこれで対処する", "将文本按照段落分隔符分割开": "テキストを段落区切り文字で分割する", "输入中可能存在乱码": "入力には文字化けが含まれる可能性があります", "重置文件的创建时间": "ファイルの作成時間をリセットする", "使每个段落之间有两个换行符分隔": "各段落の間に2つの改行を挿入する", "读取PDF文件": "PDFファイルを読み込む", "紫罗兰色": "バイオレット", "如果有": "ある場合", "使用markdown表格输出结果": "markdownテーブルを使用して結果を出力する", "不要修改!!": "修正しないでください!!", "的方式启动": "の方法で起動する", "循环轮询各个线程是否执行完毕": "各スレッドが完了したかどうかを繰り返しポーリングする", "大部分时候仅仅为了fancy的视觉效果": "ほとんどの場合、見栄えの良い視覚効果のためだけです", "结尾除去一次": "最後に1回除去する", "天蓝色": "スカイブルー", "原文": "原文", "远程返回错误": "リモートエラーが返されました", "功能区显示开关与功能区的互动": "機能エリアの表示スイッチと機能エリアの相互作用", "生成一个请求线程": "リクエストスレッドを生成する", "放弃": "放棄する", "config_private.py放自己的秘密如API和代理网址": "config_private.pyに自分のAPIやプロキシアドレスなどの秘密を入力する", "完成全部响应": "すべての応答を完了する", "将双空行": "2つの空行を挿入する", "第二层列表是对话历史": "2番目のリストは会話履歴です", "例如 v2**y 和 ss* 的默认本地协议是socks5h": "たとえば、v2 ** yとss *のデフォルトのローカルプロトコルはsocks5hです", "此版本使用pdfminer插件": "このバージョンではpdfminerプラグインが使用されています", "下载中": "ダウンロード中", "多线程润色开始": "マルチスレッドの改善が開始されました", "这个函数是用来获取指定目录下所有指定类型": "この関数は、指定されたディレクトリ内のすべての指定されたタイプを取得するために使用されます", "如果要使用jittorllms": "jittorllmsを使用する場合", "可以多线程并行": "マルチスレッド並列処理が可能です", "HotReload 的意思是热更新": "HotReloadの意味はホット更新です", "失败": "失敗しました", "proxies格式错误": "プロキシの形式が正しくありません", "您可能选择了错误的模型或请求源": "間違ったモデルまたはリクエストソースを選択した可能性があります", "内容太长了都会触发token数量溢出的错误": "コンテンツが長すぎると、トークン数がオーバーフローするエラーが発生する可能性があります", "建议": "提案する", "可能需要一点时间下载参数": "パラメータのダウンロードに少し時間がかかる場合があります", "这里是特殊函数插件的高级参数输入区": "ここは特殊関数プラグインの高度なパラメータ入力エリアです", "ChatGPT综合": "ChatGPT総合", "等待多线程操作": "マルチスレッド操作を待機しています", "按Shift+Enter换行": "Shift + Enterで改行", "inputs 是本次问询的输入": "inputsは今回の問い合わせの入力です", "单$包裹begin命令时多余": "beginコマンドを単一の$で囲むと余分になります", "NEWBING_COOKIES未填写或有格式错误": "NEWBING_COOKIESが入力されていないか、形式が正しくありません", "直接取出来": "直接取り出す", "懂的都懂": "理解できる人は理解する", "常规情况下": "通常の場合", "给出输出文件清单": "出力ファイルリストを提供する", "如果OpenAI不响应": "OpenAIが応答しない場合", "尽可能多地保留文本": "テキストをできるだけ多く保持する", "对话历史列表": "会話履歴リスト", "不可多线程": "マルチスレッドはできません", "解析整个CSharp项目": "CSharpプロジェクト全体を解析する", "此线程失败前收到的回答": "このスレッドが失敗する前に受け取った回答", "等待MOSS响应中": "MOSSの応答を待っています", "对每一个源代码文件": "各ソースコードファイルに対して", "爬取搜索引擎的结果": "検索エンジンの結果をクロールする", "找不到任何.tex或pdf文件": ".texまたはpdfファイルが見つかりません", "AutoGPT是什么": "AutoGPTとは何ですか", "空空如也的输入栏": "空の入力欄", "除了基础的pip依赖以外": "基本的なpip依存関係以外", "你必须使用Markdown表格": "Markdownテーブルを使用する必要があります", "该函数面向希望实现更多有趣功能的开发者": "この関数は、より多くの面白い機能を実装したい開発者を対象としています", "需要访问谷歌": "Googleにアクセスする必要があります", "5s之后重启": "5秒後に再起動します", "删除其中的所有注释": "すべてのコメントを削除する", "、地址": "、アドレス", "请使用Markdown": "Markdownを使用してください", "文件代码是": "ファイルのコードは", "洋红色": "マゼンタ", "已配置": "設定済み", "分析用户提供的谷歌学术": "ユーザーが提供したGoogle Scholarの分析", "句子结束标志": "文の終わりのマーク", "尝试导入依赖": "依存関係のインポートを試みる", "authors获取失败": "著者の取得に失敗しました", "发送至chatGPT": "chatGPTに送信", "添加一个萌萌的看板娘": "かわいい看板娘を追加する", "记录删除注释后的文本": "コメントを削除したテキストを記録する", "在读取API_KEY时": "API_KEYの読み取り時", "每一块": "各ブロック", "避免解析压缩文件": "圧縮ファイルの解析を避ける", "接下来请你逐文件分析下面的论文文件": "次に、論文ファイルを1つずつ分析してください", "Endpoint 重定向": "エンドポイントのリダイレクト", "截断重试": "切り捨て再試行", "限制的3/4时": "制限の3/4時", "Windows上还需要安装winrar软件": "Windowsにはwinrarソフトウェアのインストールが必要です", "插件": "プラグイン", "输入过长已放弃": "入力が長すぎるため、放棄しました", "界面更新": "インターフェースの更新", "每个子任务的输出汇总": "各サブタスクの出力の集計", "翻译摘要等": "要約などを翻訳する", "网络卡顿、代理失败、KEY失效": "ネットワークの遅延、プロキシの失敗、KEYの無効化", "前情提要": "前提の要約", "additional_fn代表点击的哪个按钮": "additional_fnは、クリックされたボタンを表します", "再点击按钮": "ボタンを再度クリック", "等待回复": "返信を待つ", "$c$是光速": "$c$は光速です", "触发重置": "リセットをトリガーする", "借鉴了 https": "httpsを参考にしました", "追加历史": "履歴を追加する", "就是临时文件夹的路径": "一時フォルダのパスです", "开始正式执行任务": "タスクを正式に実行する", "第一种情况": "1つ目の場合", "对从 PDF 提取出的原始文本进行清洗和格式化处理": "PDFから抽出された元のテキストをクリーニングおよびフォーマット処理する", "请结合互联网信息回答以下问题": "以下の問題にインターネット情報を組み合わせて回答してください", "请你阅读以下学术论文相关的材料": "以下の学術論文に関連する資料を読んでください", "注意": "注意", "由于请求gpt需要一段时间": "GPTのリクエストには時間がかかるため", "可以直接修改对话界面内容": "対話インターフェースの内容を直接変更できます", "系统输入": "システム入力", "包括": "含む", "效果奇好": "効果が非常に良い", "配置其Path环境变量": "そのPath環境変数を設定する", "如温度和top_p等": "温度やtop_pなど", "可选 ↓↓↓": "選択可能 ↓↓↓", "代理可能无效": "プロキシは無効かもしれません", "例如": "例えば", "青色": "青色", "一言以蔽之": "一言で言えば", "直接给定文件": "ファイルを直接指定する", "分组+迭代处理": "グループ化+反復処理", "文件上传区": "ファイルアップロードエリア", "3. 如果余量太小了": "3. もし余剰が少なすぎる場合", "执行时": "実行時", "localhost意思是代理软件安装在本机上": "localhostは、プロキシソフトウェアがローカルマシンにインストールされていることを意味します", "下面是对每个参数和返回值的说明": "以下は各パラメーターおよび戻り値の説明です", "存档文件详情": "アーカイブファイルの詳細", "找不到任何.ipynb文件": "IPython Notebookファイルが見つかりません", "里面包含以指定类型为后缀名的所有文件的绝对路径": "指定されたタイプの拡張子を持つすべてのファイルの絶対パスを含む", "个片段": "フラグメント", "Index 2 框框": "インデックス2フレーム", "更换LLM模型/请求源": "LLMモデル/リクエストソースの変更", "安装Newbing的依赖": "Newbingの依存関係のインストール", "不会实时显示在界面上": "リアルタイムで画面に表示されない", "第2步": "ステップ2", "有$标识的公式符号": "$記号を持つ数式記号", "读Tex论文写摘要": "Tex論文を読んで要約を書く", "不详": "詳細不明", "也可以直接是": "直接であることもできます", "找不到任何CSharp文件": "CSharpファイルが見つかりません", "输入其他/无输入+回车=不更新": "他の入力/入力なし+ Enter = 更新しない", "然后再写一段英文摘要": "そして、もう一つの英文要約を書く", "捕捉函数f中的异常并封装到一个生成器中返回": "関数fで例外をキャッチして、ジェネレータにエンコードして返す", "重试几次": "数回リトライする", "线程": "スレッド", "程序终止": "プログラムの終了", "用户提示": "ユーザーヒント", "条": "条項", "刷新界面用 yield from update_ui": "UIを更新するには、yield from update_uiを使用します", "如何理解传奇?": "伝説を理解するには？", "请避免混用多种jittor模型": "複数のjittorモデルを混在させないでください", "说": "言う", "您可以请再次尝试.": "もう一度お試しください。", "尝试识别section": "セクションを識別しようとしています", "警告！被保存的对话历史可以被使用该系统的任何人查阅": "警告！保存された対話履歴は、このシステムを使用する誰でも閲覧できます", "Index 1 字体": "フォント1のインデックス", "分解代码文件": "コードファイルの分解", "越新越好": "新しいほど良い", "当历史上下文过长时": "履歴のコンテキストが長すぎる場合", "这是第": "これは第", "网络代理状态": "ネットワークプロキシの状態", "用于数据流可视化": "データフローの可視化に使用される", "整理history": "履歴の整理", "一-鿿": "一-鿿", "所有文件都总结完成了吗": "すべてのファイルが要約されていますか？", "默认False": "デフォルトはFalse", "这是必应": "これはBingです", "子进程Worker": "サブプロセスWorker", "重试中": "再試行中", "正常对话时使用": "通常の会話時に使用する", "直接清除历史": "履歴を直接クリアする", "处理数据流的主体": "データフローの本体を処理する", "试着补上后个": "後のものを試してみてください", "功能、贡献者": "機能、貢献者", "请先转化为.docx格式": "まず.docx形式に変換してください", "可用clear将其清空": "clearを使用してクリアできます", "需要预先pip install rarfile": "rarfileを事前にpip installする必要があります", "输入已识别为openai的api_key": "openaiのapi_keyとして認識された入力", "先上传存档或输入路径": "アーカイブをアップロードするか、パスを入力してください", "则先将公式转换为HTML格式": "公式をHTML形式に変換してください", "需要读取和清理文本的pdf文件路径": "テキストを読み取り、クリーンアップする必要があるpdfファイルのパス", "自动定位": "自動位置決め", "api2d 正常完成": "api2dが正常に完了しました", "获取页面上的文本信息": "ページからテキスト情報を取得する", "日": "日", "已经对该文章的所有片段总结完毕": "記事のすべてのセグメントを要約しました", "搜集初始信息": "初期情報を収集する", "本组文件为": "このグループのファイルは", "正常": "正常", "比如introduction": "例えば、導入", "并在被装饰的函数上执行": "デコレートされた関数で実行する", "文件路径列表": "ファイルパスリスト", "由于输入长度限制": "入力長の制限のため", "祖母绿": "エメラルドグリーン", "并替换为空字符串": "空の文字列に置き換える", "存入": "保存する", "OpenAI绑定信用卡可解除频率限制": "OpenAIはクレジットカードをバインドして頻度制限を解除できます", "获取预处理函数": "前処理関数を取得する", "Bad forward key. API2D账户额度不足": "不正なフォワードキー。API2Dアカウントの残高が不足しています", "源文件太多": "ソースファイルが多すぎます", "谷歌学术检索助手": "Google学術検索アシスタント", "方法则会被调用": "メソッドが呼び出されます", "默认是.md": "デフォルトは.mdです", "请开始多线程操作": "マルチスレッド操作を開始してください", "蓝色": "青色", "如果是网络上的文件": "ネットワーク上のファイルの場合", "开始下一个循环": "次のループを開始する", "更换模型 & SysPrompt & 交互界面布局": "モデルの変更＆SysPrompt＆インタラクティブインターフェイスレイアウト", "二、论文翻译": "2.論文翻訳", "再失败就没办法了": "もう失敗したらどうしようもない", "解析整个Java项目": "Javaプロジェクト全体を解析する", "只裁剪历史": "履歴のトリミングのみ", "基础功能区": "基本機能エリア", "gradio可用颜色列表": "利用可能なGradioの色のリスト", "的高级参数说明": "高度なパラメータの説明", "是否在arxiv中": "arxivにあるかどうか", "提交": "提出", "回车退出": "Enterで終了", "详情见get_full_error的输出": "get_full_errorの出力を参照してください", "您可以随时在history子文件夹下找回旧版的程序": "いつでもhistoryサブフォルダーで以前のバージョンのプログラムを取得できます", "手动指定和筛选源代码文件类型": "ソースコードファイルタイプを手動で指定およびフィルタリングする", "更多函数插件": "その他の関数プラグイン", "看门狗的耐心": "監視犬の忍耐力", "然后yield出去": "そして出力する", "拆分过长的IPynb文件": "長すぎるIPynbファイルを分割する", "1. 把input的余量留出来": "1. 入力の余裕を残す", "请求超时": "リクエストがタイムアウトしました", "是之前的对话列表": "以前の会話リストです", "有些文章的正文部分字体大小不是100%统一的": "一部の記事の本文のフォントサイズが100％統一されていない場合があります", "加载参数": "パラメータをロードする", "在汇总报告中隐藏啰嗦的真实输入": "冗長な実際の入力をサマリーレポートで非表示にする", "获取完整的从Openai返回的报错": "Openaiから返された完全なエラーを取得する", "灰色": "グレー", "表示要搜索的文件类型": "検索するファイルタイプを示します", "亲人两行泪": "家族の2行の涙", "等待NewBing响应中": "NewBingの応答を待っています", "请复制并转到以下URL": "以下のURLをコピーして移動してください", "开始接收chatglm的回复": "chatglmの返信を受け取り始めます", "第6步": "ステップ6", "可调节线程池的大小避免openai的流量限制错误": "OpenAIのトラフィック制限エラーを回避するためにスレッドプールのサイズを調整できます", "等待响应": "レスポンスを待っています", "月": "月", "裁剪时": "トリミング中", "异步任务结束": "非同期タスクが終了しました", "正在处理中": "処理中", "润色": "校正中", "提取精炼信息": "情報の抽出と精製", "您可以试试让AI写一个Related Works": "AIにRelated Worksを書かせてみることができます", "主进程统一调用函数接口": "メインプロセスが関数インターフェースを統一的に呼び出します", "再例如一个包含了待处理文件的路径": "処理待ちのファイルを含むパスの例", "负责把学术论文准确翻译成中文": "学術論文を正確に中国語に翻訳する責任があります", "函数的说明请见 request_llms/bridge_all.py": "関数の説明については、request_llms/bridge_all.pyを参照してください", "然后回车提交": "そしてEnterを押して提出してください", "防止爆token": "トークンの爆発を防止する", "Latex项目全文中译英": "LaTeXプロジェクト全文の中国語から英語への翻訳", "递归地切割PDF文件": "PDFファイルを再帰的に分割する", "使用该模块需要额外依赖": "このモジュールを使用するには、追加の依存関係が必要です", "放到history中": "履歴に保存する", "汇总报告如何远程获取": "サマリーレポートをリモートで取得する方法", "清空历史": "履歴をクリアする", "代理所在地查询超时": "プロキシの場所のクエリがタイムアウトしました", "列表": "リスト", "检测到程序终止": "プログラムの終了が検出されました", "重命名文件": "ファイル名を変更する", "用&符号分隔": "&記号で分割する", "LLM的内部调优参数": "LLMの内部チューニングパラメータ", "建议您复制一个config_private.py放自己的秘密": "config_private.pyをコピーして、自分の秘密を入れてください", "$m$是质量": "質量を表します", "具备多线程调用能力的函数": "マルチスレッド呼び出し機能を備えた関数", "将普通文本转换为Markdown格式的文本": "通常のテキストをMarkdown形式のテキストに変換する", "rar和7z格式正常": "rarおよび7z形式が正常である", "使用wraps": "wrapsを使用する", "带超时倒计时": "タイムアウトカウントダウン付き", "准备对工程源代码进行汇总分析": "プロジェクトソースコードの集計分析を準備する", "未知": "不明", "第n组插件": "n番目のプラグイン", "ChatGLM响应异常": "ChatGLMの応答が異常です", "使用Unsplash API": "Unsplash APIを使用する", "读取默认值作为数据类型转换的参考": "デフォルト値を読み取り、データ型変換の参考にする", "请更换为API_URL_REDIRECT配置": "API_URL_REDIRECT構成に変更してください", "青蓝色": "青色と青緑色", "如果中文效果不理想": "中国語の効果が理想的でない場合", "Json异常": "Json例外", "chatglm 没有 sys_prompt 接口": "chatglmにはsys_promptインターフェースがありません", "停止": "停止", "的文件": "のファイル", "可能处于折叠状态": "折りたたみ状態になっている可能性があります", "但还没输出完后面的": "しかし、まだ後ろの出力が完了していません", "单线程方法": "シングルスレッドメソッド", "不支持通过环境变量设置!": "環境変数を介して設定することはできません！", "“喂狗”": "「犬に餌をやる」", "获取设置": "設定を取得する", "Json解析不合常规": "<PERSON>son解析が通常と異なる", "请对下面的程序文件做一个概述文件名是": "以下のプログラムファイルについて概要を説明してください。ファイル名は", "输出": "出力", "这个函数用stream的方式解决这个问题": "この関数はストリームを使用してこの問題を解決します", "根据 heuristic 规则": "ヒューリスティックルールに従って", "假如重启失败": "再起動に失敗した場合", "然后在用常规的": "その後、通常の方法を使用する", "加入下拉菜单中": "ドロップダウンメニューに追加する", "正在分析一个项目的源代码": "プロジェクトのソースコードを分析しています", "从以上搜索结果中抽取信息": "上記の検索結果から情報を抽出する", "安全第一条": "安全が最優先です", "并相应地进行替换": "適切に置換する", "第5次尝试": "5回目の試み", "例如在windows cmd中": "例えば、Windowsのcmdで", "打开你的*学*网软件查看代理的协议": "あなたの*学*ウェブソフトウェアを開いて、プロキシプロトコルを確認する", "用多种方式组合": "複数の方法を組み合わせる", "找不到任何.h头文件": ".hヘッダーファイルが見つかりません", "是本次问询的输入": "この問い合わせの入力です", "并替换为回车符": "改行文字に置換する", "不能自定义字体和颜色": "フォントと色をカスタマイズできません", "点击展开“文件上传区”": "「ファイルアップロードエリア」をクリックして展開する", "高危设置！通过修改此设置": "高危険設定！この設定を変更することで", "开始重试": "再試行を開始する", "你是一个学术翻译": "あなたは学術翻訳者です", "表示要搜索的文件或者文件夹路径或网络上的文件": "検索するファイルまたはフォルダのパスまたはネットワーク上のファイルを示す", "没办法了": "どうしようもない", "优先级3. 获取config中的配置": "優先度3. configから設定を取得する", "读取配置文件": "設定ファイルを読み込む", "查询版本和用户意见": "バージョンとユーザーの意見を検索する", "提取摘要": "要約を抽出する", "在gpt输出代码的中途": "GPTがコードを出力する途中で", "如1024x1024": "1024x1024のように", "概括其内容": "内容を要約する", "剩下的情况都开头除去": "残りの場合はすべて先頭を除去する", "至少一个线程任务意外失败": "少なくとも1つのスレッドタスクが予期しない失敗をした", "完成情况": "完了状況", "输入栏用户输入的文本": "入力欄にユーザーが入力したテキスト", "插件调度异常": "プラグインスケジューリングの例外", "插件demo": "プラグインデモ", "chatGPT分析报告": "chatGPT分析レポート", "以下配置可以优化体验": "以下の設定で体験を最適化できます", "是否一键更新代码": "コードをワンクリックで更新するかどうか", "pip install pywin32 用于doc格式": "doc形式に使用するためのpip install pywin32", "如果同时InquireMultipleLargeLanguageModels": "同時にInquireMultipleLargeLanguageModelsを使用する場合", "整理反复出现的控件句柄组合": "繰り返し出現するコントロールハンドルの組み合わせを整理する", "可能会导致严重卡顿": "重度のカクつきを引き起こす可能性がある", "程序完成": "プログラム完了", "在装饰器内部": "デコレーターの内部で", "函数插件功能": "関数プラグイン機能", "把完整输入-输出结果显示在聊天框": "完全な入力-出力結果をチャットボックスに表示する", "对全文进行概括": "全文を要約する", "HotReload的装饰器函数": "HotReloadのデコレーター関数", "获取tokenizer": "tokenizerを取得する", "则随机选取WEB端口": "WEBポートをランダムに選択する", "解析项目": "プロジェクトを解析する", "并且不要有反斜线": "そしてバックスラッシュを含めないでください", "汇总报告已经添加到右侧“文件上传区”": "サマリーレポートはすでに右側の「ファイルアップロードエリア」に追加されています", "装饰器函数返回内部函数": "デコレーター関数は内部関数を返します", "根据以上你自己的分析": "上記の分析に基づいて自分自身を分析する", "只输出代码": "コードのみを出力する", "并执行函数的新版本": "関数の新バージョンを実行する", "请不吝PR！": "PRを遠慮なく提出してください！", "你好": "こんにちは", "或者您没有获得体验资格": "またはあなたは体験資格を持っていない", "temperature是chatGPT的内部调优参数": "temperatureはchatGPTの内部調整パラメータです", "结果写入文件": "結果をファイルに書き込む", "输入区": "入力エリア", "这段代码定义了一个名为DummyWith的空上下文管理器": "このコードは、DummyWithという名前の空のコンテキストマネージャを定義しています", "加载需要一段时间": "読み込みには時間がかかります", "和端口": "およびポート", "当你想发送一张照片时": "写真を送信したい場合", "为了更好的效果": "より良い効果を得るために", "逻辑较乱": "ロジックがやや乱雑です", "调用路径参数已自动修正到": "呼び出しパスのパラメータが自動的に修正されました", "地址🚀": "アドレス🚀", "也可以获取它": "それを取得することもできます", "pip install python-docx 用于docx格式": "pip install python-docxはdocx形式に使用されます", "该模板可以实现ChatGPT联网信息综合": "このテンプレートは、ChatGPTネットワーク情報の総合を実現できます", "的标识": "のマーク", "取决于": "に依存する", "ChatGLM尚未加载": "ChatGLMはまだロードされていません", "处理多模型并行等细节": "複数のモデルの並列処理などの詳細を処理する", "代理与自动更新": "プロキシと自動更新", "摘要在 .gs_rs 中的文本": ".gs_rs中の要約テキスト", "补上后面的": "後ろに補完する", "输入了已经经过转化的字符串": "変換済みの文字列が入力されました", "对整个Latex项目进行润色": "全体のLatexプロジェクトを磨き上げる", "即将更新pip包依赖……": "pipパッケージ依存関係を更新する予定...", "ダウンロードしたpdfファイルが失敗しました": "PDFファイルのダウンロードに失敗しました", "何もありません": "何もありません", "次の文字が大文字である場合": "次の文字が大文字である場合", "yield一次以刷新前端页面": "フロントエンドページを更新するためにyieldを1回実行します", "入力部分が自由すぎる": "入力部分が自由すぎる", "中文Latex项目全文润色": "中国語のLatexプロジェクトの全文を校正する", "ファイルを読み込む": "ファイルを読み込む", "プライバシー保護に注意してください！": "プライバシー保護に注意してください！", "ただし、途中でネットワークケーブルが切断されることを避けるために内部でストリームを使用する": "ただし、途中でネットワークケーブルが切断されることを避けるために内部でストリームを使用する", "上下レイアウト": "上下レイアウト", "historyは以前の会話リストです": "historyは以前の会話リストです", "pdfファイルを読み込む": "pdfファイルを読み込む", "同時に長い文を分解する": "同時に長い文を分解する", "Unsplash APIを使用する": "Unsplash APIを使用する", "各llmモデルに単体テストを実行する": "各llmモデルに単体テストを実行する", "ローカルで使用する場合はお勧めしません": "ローカルで使用する場合はお勧めしません", "亜鉛色": "亜鉛色", "論文": "論文", "1つの大規模言語モデルのみに問い合わせる場合": "1つの大規模言語モデルのみに問い合わせる場合", "会話履歴": "会話履歴", "入力をトリミングする": "入力をトリミングする", "第2部分": "第2部分", "gpt4は現在、申請が承認された人のみに公開されています": "gpt4は現在、申請が承認された人のみに公開されています", "以下は学術論文の基本情報です": "以下は学術論文の基本情報です", "出力が不完全になる原因となる": "出力が不完全になる原因となる", "ハイフンを使って": "ハイフンを使って", "请先把模型切换至gpt-xxxx或者api2d-xxxx": "Please switch the model to gpt-xxxx or api2d-xxxx first.", "路径或网址": "Path or URL", "*代表通配符": "* represents a wildcard", "块元提取": "Block element extraction", "使用正则表达式查找注释": "Use regular expressions to find comments", "但推荐上传压缩文件": "But it is recommended to upload compressed files", "实现更换API_URL的作用": "Implement the function of changing API_URL", "从摘要中提取高价值信息": "Extract high-value information from the summary", "警告": "Warning", "ChatGLM消耗大量的内存": "ChatGLM consumes a lot of memory", "历史中哪些事件发生在": "Which events happened in history", "多线": "Multi-threaded", "石头色": "Stone color", "NewBing响应缓慢": "NewBing responds slowly", "生成一份任务执行报告": "Generate a task execution report", "用空格或段落分隔符替换原换行符": "Replace the original line break with a space or paragraph separator", "其他小工具": "Other small tools", "当前问答": "Current Q&A", "支持任意数量的llm接口": "Support any number of llm interfaces", "在传递chatbot的过程中不要将其丢弃": "Do not discard it in the process of passing chatbot", "2. 把输出用的余量留出来": "2. Leave room for the output", "稍后可能需要再试一次": "May need to try again later", "显示/隐藏功能区": "Show/hide the function area", "拆分过长的latex文件": "Split overly long latex files", "子进程执行": "Subprocess execution", "排除了以上两个情况": "Excludes the above two cases", "您将把您的API-KEY和对话隐私完全暴露给您设定的中间人！": "You will completely expose your API-KEY and conversation privacy to the intermediary you set!", "表示文件所在的文件夹路径": "Indicates the folder path where the file is located", "获取正文主字体": "本文フォントを取得する", "中文学术润色": "中国語の学術的な磨きをかける", "i_say_show_user=给用户看的提问": "ユーザーに表示される質問", "需要清除首尾空格": "先頭と末尾の空白を削除する必要があります", "请你作为一个学术翻译": "学術翻訳者としてお願いします", "中译英": "中国語から英語への翻訳", "chatGPT的内部调优参数": "chatGPTの内部調整パラメータ", "test_解析一个Cpp项目": "Cppプロジェクトの解析をテストする", "默认开启": "デフォルトで有効になっています", "第三方库": "サードパーティのライブラリ", "如果需要在二级路径下运行": "2次パスで実行する必要がある場合", "chatGPT 分析报告": "chatGPT分析レポート", "不能正常加载ChatGLM的参数！": "ChatGLMのパラメータを正常にロードできません！", "并定义了一个名为decorated的内部函数": "内部関数decoratedを定義しました", "所有线程同时开始执行任务函数": "すべてのスレッドが同時にタスク関数を開始します", "Call jittorllms fail 不能正常加载jittorllms的参数": "jittorllmsのパラメータを正常にロードできません", "任何文件": "任意のファイル", "分解连字": "リガチャの分解", "如果子任务非常多": "サブタスクが非常に多い場合", "如果要使用ChatGLM": "ChatGLMを使用する場合", "**函数功能**": "**関数の機能**", "等待jittorllms响应中": "jittorllmsの応答を待っています", "查找语法错误": "構文エラーを検索する", "尝试识别段落": "段落を認識しようとする", "下载PDF文档": "PDF文書をダウンロードする", "搜索页面中": "ページ内を検索する", "然后回车键提交后即可生效": "Enterキーを押して送信すると有効になります", "请求处理结束": "リクエスト処理が終了しました", "按钮见functional.py": "functional.pyにあるボタン", "提交按钮、重置按钮": "送信ボタン、リセットボタン", "网络错误": "ネットワークエラー", "第10步": "10番目のステップ", "问号": "質問符", "两个指令来安装jittorllms的依赖": "jittorllmsの依存関係をインストールするための2つの命令", "询问多个GPT模型": "複数のGPTモデルについて問い合わせる", "增强报告的可读性": "レポートの可読性を向上させる", "如果缺少依赖": "依存関係が不足している場合", "比如你是翻译官怎样怎样": "例えば、あなたが翻訳者である場合の方法", "MOSS尚未加载": "MOSSがまだロードされていません", "第一部分": "第1部分", "的分析如下": "の分析は以下の通りです", "解决一个mdx_math的bug": "mdx_mathのバグを解決する", "函数插件输入输出接驳区": "関数プラグインの入出力接続エリア", "打开浏览器": "ブラウザを開く", "免费用户填3": "無料ユーザーは3を入力してください", "版": "版", "不需要重启程序": "プログラムを再起動する必要はありません", "正在查找对话历史文件": "会話履歴ファイルを検索しています", "内部函数通过使用importlib模块的reload函数和inspect模块的getmodule函数来重新加载并获取函数模块": "内部関数は、importlibモジュールのreload関数とinspectモジュールのgetmodule関数を使用して、関数モジュールを再ロードおよび取得します", "解析整个C++项目": "C++プロジェクト全体を解析する", "函数热更新是指在不停止程序运行的情况下": "関数のホットアップデートとは、プログラムの実行を停止せずに行うことを指します", "代码高亮": "コードのハイライト", "否则在回复时会因余量太少出问题": "そうしないと、返信時に余裕が少なすぎて問題が発生する可能性があります", "该函数详细注释已添加": "この関数には詳細な注釈が追加されました", "默认允许多少路线程同时访问OpenAI": "デフォルトでOpenAIに同時にアクセスできるスレッド数はいくつですか", "网络的远程文件": "リモートファイルのネットワーク", "搜索需要处理的文件清单": "処理する必要のあるファイルリストを検索する", "提交任务": "タスクを提出する", "根据以上的对话": "上記の対話に基づいて", "提示": "ヒント", "然后重试": "その後、再試行してください", "只输出转化后的英文代码": "変換後の英語コードのみを出力する", "GPT返回的结果": "GPTが返す結果", "您的 API_KEY 是": "あなたのAPI_KEYは", "给gpt的静默提醒": "GPTに対するサイレントリマインダー", "先寻找到解压的文件夹路径": "解凍されたフォルダのパスを最初に検索する", "”补上": "補う", "清除重复的换行": "重複する改行をクリアする", "递归": "再帰", "把已经获取的数据显示出去": "取得したデータを表示する", "参数": "パラメータ", "已完成": "完了しました", "方法会在代码块被执行前被调用": "メソッドはコードブロックが実行される前に呼び出されます", "第一次运行": "最初の実行", "does not exist. 模型不存在": "存在しません。モデルが存在しません", "每个子任务展现在报告中的输入": "レポートに表示される各サブタスクの入力", "response中会携帯traceback报错信息": "responseにはtracebackエラー情報が含まれます", "在实验过程中发现调用predict_no_ui处理长文档时": "実験中に、predict_no_uiを呼び出して長いドキュメントを処理することがわかりました", "发送图片时": "画像を送信するとき", "如果换行符前为句子结束标志": "改行記号の前に文の終わりの記号がある場合", "获取图片URL": "画像のURLを取得する", "提取字体大小是否近似相等": "フォントサイズを抽出して近似しているかどうかを確認する", "填写之前不要忘记把USE_PROXY改成True": "記入する前に、USE_PROXYをTrueに変更することを忘れないでください", "列举两条并发送相关图片": "List two and send related pictures", "第一层列表是子任务分解": "The first level list is subtask decomposition", "把newbing的长长的cookie放到这里": "Put <PERSON><PERSON>'s long cookie here", "不输入即全部匹配": "No input means all matches", "不输入代表全部匹配": "No input means all matches", "请对下面的文章片段用中文做一个概述": "Please summarize the following article fragment in Chinese", "迭代之前的分析": "Analysis before iteration", "返回一个新的字符串": "Return a new string", "可同时填写多个API-KEY": "Multiple API-KEYs can be filled in at the same time", "乱七八糟的后处理": "Messy post-processing", "然后回答问题": "Then answer the question", "是否唤起高级插件参数区": "Whether to call the advanced plugin parameter area", "判定为不是正文": "Determined as not the main text", "输入区2": "Input area 2", "来自EdgeGPT.py": "From EdgeGPT.py", "解释代码": "Explain the code", "直接在输入区键入api_key": "Enter the api_key directly in the input area", "文章内容是": "The content of the article is", "也可以在问题输入区输入临时的api-key": "You can also enter a temporary api-key in the question input area", "不需要高级参数": "No advanced parameters required", "下面是一些学术文献的数据": "Below are some data on academic literature", "整理结果": "Organized results", "不能加载Newbing组件": "Cannot load Newbing component", "仅仅服务于视觉效果": "Only serves visual effects", "主进程执行": "Main process execution", "请耐心完成后再提交新问题": "Please submit a new question after completing it patiently", "找不到任何.docx或doc文件": "Cannot find any .docx or .doc files", "修改函数插件代码后": "After modifying the function plugin code", "TGUI不支持函数插件的实现": "TGUIは関数プラグインの実装をサポートしていません", "不要修改任何LaTeX命令": "LaTeXコマンドを変更しないでください", "安装方法": "インストール方法", "退出": "終了", "由于您没有设置config_private.py私密配置": "config_private.pyのプライベート設定が設定されていないため", "查询代理的地理位置": "プロキシの地理的位置を検索する", "Token限制下的截断与处理": "トークン制限下の切り捨てと処理", "python 版本建议3.9+": "Pythonバージョン3.9+を推奨します", "如果是.doc文件": ".docファイルの場合", "跨平台": "クロスプラットフォーム", "输入谷歌学术搜索页url": "Google Scholar検索ページのURLを入力してください", "高级参数输入区的显示提示": "高度なパラメータ入力エリアの表示ヒント", "找不到任何.md文件": ".mdファイルが見つかりません", "请对下面的文章片段用中文做概述": "以下の記事の断片について、中国語で概要を説明してください", "用户界面对话窗口句柄": "ユーザーインターフェースの対話ウィンドウハンドル", "chatGPT对话历史": "chatGPTの対話履歴", "基础功能区的回调函数注册": "基本機能エリアのコールバック関数の登録", "根据给定的匹配结果来判断换行符是否表示段落分隔": "与えられた一致結果に基づいて、改行記号が段落の区切りを表すかどうかを判断する", "第2次尝试": "2回目の試み", "布尔值": "ブール値", "您既可以在config.py中修改api-key": "config.pyでapi-keyを変更することができます", "清理后的文本内容字符串": "クリーンアップされたテキストコンテンツ文字列", "去除短块": "短いブロックを削除する", "利用以上信息": "上記情報を利用する", "从而达到实时更新功能": "これにより、リアルタイム更新機能が実現されます", "第5步": "5番目のステップ", "载入对话历史文件": "対話履歴ファイルを読み込む", "修改它": "それを変更する", "正在执行一些模块的预热": "モジュールのプレウォームを実行しています", "避免包括解释": "解釈を含めないようにする", "使用 lru缓存 加快转换速度": "変換速度を高速化するためにlruキャッシュを使用する", "与gradio版本和网络都相关": "gradioバージョンとネットワークに関連しています", "以及代理设置的格式是否正确": "およびプロキシ設定の形式が正しいかどうか", "OpenAI所允许的最大并行过载": "OpenAIが許可する最大並列過負荷", "代码开源和更新": "コードのオープンソース化と更新", "网络等出问题时": "ネットワークなどに問題が発生した場合", "1、英文题目；2、中文题目翻译；3、作者；4、arxiv公开": "1.英語のタイトル；2.中国語のタイトルの翻訳；3.著者；4.arxiv公開", "发送 GET 请求": "GETリクエストを送信する", "向chatbot中添加简单的意外错误信息": "チャットボットに簡単な予期しないエラーメッセージを追加する", "代理配置": "プロキシの設定", "这个函数运行在主进程": "この関数はメインプロセスで実行されます", "找不到任何lua文件": "luaファイルが見つかりません", "降低请求频率中": "リクエスト頻度を低下させる", "迭代地历遍整个文章": "記事全体を反復処理する", "否则将导致每个人的NewBing问询历史互相渗透": "さもないと、各人のNewBingクエリ履歴が相互に浸透する可能性があります", "并修改代码拆分file_manifest列表": "コードを変更して、file_manifestリストを分割する", "第 0 步": "ステップ0", "提高限制请查询": "制限を引き上げるには、クエリを確認してください", "放在这里": "ここに置いてください", "红色": "赤色", "上传本地文件可供红色函数插件调用": "ローカルファイルをアップロードして、赤い関数プラグインを呼び出すことができます", "正在加载tokenizer": "トークナイザーをロードしています", "非OpenAI官方接口的出现这样的报错": "OpenAI公式インターフェース以外でこのようなエラーが発生する", "跨线程传递": "スレッド間での伝達", "代码直接生效": "コードが直接有効になる", "基本信息": "基本情報", "默认": "#", "首先你在英文语境下通读整篇论文": "最初に、論文全体を英語で読みます", "的第": "の", "第9步": "9番目のステップ", "gpt模型参数": "GPTモデルのパラメータ", "等待": "待つ", "一次性完成": "一度に完了する", "收到以下文件": "以下のファイルを受け取りました", "生成正则表达式": "正規表現を生成する", "参数简单": "パラメータは簡単です", "设置一个token上限": "トークンの上限を設定する", "i_say=真正给chatgpt的提问": "i_say=ChatGPTに本当の質問をする", "请刷新界面重试": "ページを更新して再試行してください", "对程序的整体功能和构架重新做出概括": "プログラムの全体的な機能と構造を再概要化する", "以下是一篇学术论文中的一段内容": "以下は学術論文の一部です", "您可以调用“LoadConversationHistoryArchive”还原当下的对话": "「LoadConversationHistoryArchive」を呼び出して、現在の会話を復元できます", "读取Markdown文件": "Markdownファイルを読み込む", "最终": "最終的に", "或显存": "またはグラフィックスメモリ", "如果最后成功了": "最後に成功した場合", "例如chatglm&gpt-3.5-turbo&api2d-gpt-4": "例えば、chatglm＆gpt-3.5-turbo＆api2d-gpt-4", "使用中文回答我的问题": "中国語で私の質問に答えてください", "我需要你找一张网络图片": "インターネット上の画像を探してください", "我上传了文件": "ファイルをアップロードしました", "从而实现分批次处理": "バッチ処理を実現するため", "我们先及时地做一次界面更新": "まず、タイムリーに画面を更新します", "您还需要运行": "実行する必要があります", "该函数只有20多行代码": "その関数には20行以上のコードしかありません", "但端口号都应该在最显眼的位置上": "しかし、ポート番号は常に目立つ場所にある必要があります", "Token溢出数": "Tokenオーバーフロー数", "private_upload里面的文件名在解压zip后容易出现乱码": "private_upload内のファイル名は、zipを解凍すると文字化けしやすいです", "以下“红颜色”标识的函数插件需从输入区读取路径作为参数": "以下の「赤色」で表示された関数プラグインは、パスを入力エリアから引数として読み取る必要があります", "如果WEB_PORT是-1": "WEB_PORTが-1の場合", "防止回答时Token溢出": "回答時のTokenオーバーフローを防止する", "第三种情况": "第3の場合", "前言": "序文", "打开文件": "ファイルを開く", "用于输入给GPT的前提提示": "GPTに入力するための前提条件のヒント", "返回值": "戻り値", "请查收": "受信箱を確認してください", "看门狗": "ウォッチドッグ", "返回重试": "戻って再試行する", "裁剪input": "inputをトリミングする", "字符串": "文字列", "以下是信息源": "以下は情報源です", "你是一名专业的学术教授": "あなたは専門の学術教授です", "处理中途中止的情况": "途中で処理を中止する場合", "清除历史": "履歴をクリアする", "完成了吗": "完了しましたか", "接收文件后与chatbot的互动": "ファイルを受信した後、chatbotとのインタラクション", "插件初始化中": "プラグインの初期化中", "系统静默prompt": "システム静黙プロンプト", "上下文管理器必须实现两个方法": "コンテキストマネージャは2つのメソッドを実装する必要があります", "你需要翻译以下内容": "以下の内容を翻訳する必要があります", "的api-key": "のAPIキー", "收到消息": "メッセージを受信しました", "将插件中出的所有问题显示在界面上": "すべての問題をインターフェースに表示する", "正在提取摘要并下载PDF文档……": "要約を抽出し、PDFドキュメントをダウンロードしています...", "不能达到预期效果": "期待される効果が得られない", "清除当前溢出的输入": "現在のオーバーフロー入力をクリアする", "当文件被上传时的回调函数": "ファイルがアップロードされたときのコールバック関数", "已重置": "リセットされました", "无": "なし", "总结输出": "出力をまとめる", "第 3 步": "ステップ3", "否则可能导致显存溢出而造成卡顿": "それ以外の場合、グラフィックスメモリのオーバーフローが発生し、フリーズが発生する可能性があります", "gradio的inbrowser触发不太稳定": "Gradioのinbrowserトリガーはあまり安定していません", "发送至LLM": "LLMに送信", "异步任务开始": "非同期タスクが開始されました", "和openai的连接容易断掉": "OpenAIとの接続が簡単に切断される", "用一句话概括程序的整体功能": "プログラムの全体的な機能を一言で表す", "等待NewBing响应": "NewBingの応答を待っています", "会自动使用已配置的代理": "事前に設定されたプロキシを自動的に使用します", "带Cookies的Chatbot类": "Cookieを持つChatbotクラス", "安装MOSS的依赖": "MOSSの依存関係をインストールする", "或者": "または", "函数插件-下拉菜单与随变按钮的互动": "関数プラグイン-ドロップダウンメニューと可変ボタンの相互作用", "完成": "完了", "这段代码来源 https": "このコードの出典：https", "年份获取失败": "年を取得できませんでした", "你必须逐个文献进行处理": "文献を1つずつ処理する必要があります", "文章极长": "記事が非常に長い", "选择处理": "処理を選択する", "进入任务等待状态": "タスク待機状態に入る", "它可以作为创建新功能函数的模板": "It can serve as a template for creating new feature functions", "当前模型": "Current model", "中间过程不予显示": "Intermediate process is not displayed", "OpenAI模型选择是": "OpenAI model selection is", "故可以只分析文章内容": "So only the content of the article can be analyzed", "英语学术润色": "English academic polishing", "此key无效": "This key is invalid", "您可能需要手动安装新增的依赖库": "You may need to manually install the new dependency library", "会把traceback和已经接收的数据转入输出": "Will transfer traceback and received data to output", "后语": "Postscript", "最后用中文翻译摘要部分": "Finally, translate the abstract section into Chinese", "如果直接在海外服务器部署": "If deployed directly on overseas servers", "找不到任何前端相关文件": "No frontend-related files can be found", "Not enough point. API2D账户点数不足": "Not enough points. API2D account points are insufficient", "当前版本": "Current version", "1. 临时解决方案": "1. Temporary solution", "第8步": "Step 8", "历史": "History", "是否在结束时": "Whether to write conversation history at the end", "对话历史写入": "Write conversation history", "观测窗": "Observation window", "刷新时间间隔频率": "Refresh time interval frequency", "当输入部分的token占比": "When the token proportion of the input part is", "这是什么": "What is this", "现将您的现有配置移动至config_private.py以防止配置丢失": "Now move your existing configuration to config_private.py to prevent configuration loss", "尝试": "Try", "您也可以选择删除此行警告": "You can also choose to delete this warning line", "调用主体": "Call subject", "当前代理可用性": "Current proxy availability", "将单空行": "Single blank line", "将结果写入markdown文件中": "Write the result to a markdown file", "按输入的匹配模式寻找上传的非压缩文件和已解压的文件": "Find uploaded uncompressed files and decompressed files according to the input matching mode", "设置5秒即可": "Set for 5 seconds", "需要安装pip install rarfile来解压rar文件": "Need to install pip install rarfile to decompress rar files", "如API和代理网址": "Such as API and proxy URLs", "每个子任务的输入": "Input for each subtask", "而在上下文执行结束时": "While at the end of the context execution", "Incorrect API key. OpenAI以提供了不正确的API_KEY为由": "Incorrect API key. OpenAI cites incorrect API_KEY as the reason", "即在代码结构不变得情况下取代其他的上下文管理器": "That is, replace other context managers without changing the code structure", "递归搜索": "Recursive search", "找到原文本中的换行符": "Find line breaks in the original text", "开始了吗": "Has it started?", "地址": "Address", "将生成的报告自动投射到文件上传区": "Automatically project the generated report to the file upload area", "数据流的显示最后收到的多少个字符": "Display how many characters the data stream received last", "缺少ChatGLM的依赖": "Missing dependency for ChatGLM", "不需要修改": "No modification needed", "正在分析一个源代码项目": "Analyzing a source code project", "第7步": "Step 7", "这是什么功能": "What is this function?", "你的任务是改进所提供文本的拼写、语法、清晰、简洁和整体可读性": "Your task is to improve the spelling, grammar, clarity, conciseness, and overall readability of the provided text", "不起实际作用": "Does not have any actual effect", "不显示中间过程": "Do not display intermediate processes", "对整个Latex项目进行翻译": "Translate the entire Latex project", "在上下文执行开始的情况下": "When the context execution starts", "等待ChatGLM响应中": "ChatGLMの応答を待っています", "GPT输出格式错误": "GPTの出力形式が間違っています", "最多同时执行5个": "同時に最大5つ実行できます", "解析此项目本身": "このプロジェクト自体を解析する", "肯定已经都结束了": "もう終わったに違いない", "英文Latex项目全文润色": "英語のLatexプロジェクト全体を校正する", "修改函数插件后": "関数プラグインを変更した後", "请谨慎操作": "注意して操作してください", "等待newbing回复的片段": "newbingの返信を待っているフラグメント", "第 5 步": "5番目のステップ", "迭代上一次的结果": "前回の結果を反復処理する", "载入对话": "対話をロードする", "最后": "最後に", "在前端打印些好玩的东西": "フロントエンドで面白いものを印刷する", "用于显示给用户": "ユーザーに表示するために使用されます", "在界面上显示结果": "結果をインターフェースに表示する", "检查一下是不是忘了改config": "configを変更するのを忘れていないか確認してください", "亮色主题": "明るいテーマ", "开始请求": "リクエストを開始する", "若输入0": "0を入力する場合", "清除换行符": "改行をクリアする", "Token溢出": "トークンオーバーフロー", "靛蓝色": "藍紫色", "的主要内容": "の主な内容", "执行中": "実行中", "生成http请求": "httpリクエストを生成する", "第一页清理后的文本内容列表": "最初のページのクリーンアップされたテキストコンテンツリスト", "初始值是摘要": "初期値は要約です", "Free trial users的限制是每分钟3次": "無料トライアルユーザーの制限は、1分あたり3回です", "处理markdown文本格式的转变": "Markdownテキストのフォーマット変換", "如没有给定输入参数": "入力パラメータが指定されていない場合", "缺少MOSS的依赖": "MOSSの依存関係が不足しています", "打开插件列表": "プラグインリストを開く", "失败了": "失敗しました", "OpenAI和API2D不会走这里": "OpenAIとAPI2Dはここを通過しません", "解析整个前端项目": "フロントエンドプロジェクト全体を解析する", "将要忽略匹配的文件名": "一致するファイル名を無視する予定です", "网页的端口": "Webページのポート", "切分和重新整合": "分割と再結合", "有肉眼不可见的小变化": "肉眼では見えない微小な変化があります", "实现插件的热更新": "プラグインのホット更新を実現する", "默认值": "デフォルト値", "字符数小于100": "文字数が100未満です", "更新UI": "UIを更新する", "我们剥离Introduction之后的部分": "Introductionを削除した後の部分", "注意目前不能多人同时调用NewBing接口": "現時点では、複数のユーザーが同時にNewBing APIを呼び出すことはできません", "黄色": "黄色", "中提取出“标题”、“收录会议或期刊”等基本信息": "タイトル、収録会議またはジャーナルなどの基本情報を抽出する", "NewBing响应异常": "NewBingの応答が異常です", "\\cite和方程式": "\\citeと方程式", "则覆盖原config文件": "元のconfigファイルを上書きする", "Newbing失败": "Newbingが失敗しました", "需要预先pip install py7zr": "事前にpip install py7zrが必要です", "换行 -": "改行 -", "然后通过getattr函数获取函数名": "その後、getattr関数を使用して関数名を取得します", "中性色": "中性色", "直到历史记录的标记数量降低到阈值以下": "直到履歴のマーク数が閾値以下になるまで", "请按以下描述给我发送图片": "以下の説明に従って画像を送信してください", "用学术性语言写一段中文摘要": "学術的な言葉で中国語の要約を書く", "开发者们❤️": "開発者たち❤️", "解析整个C++项目头文件": "C++プロジェクトのヘッダーファイル全体を解析する", "将输入和输出解析为HTML格式": "入力と出力をHTML形式で解析する", "重试一次": "もう一度やり直す", "如1812.10695": "例えば1812.10695のように", "当无法用标点、空行分割时": "句読点や空行で区切ることができない場合", "第二步": "2番目のステップ", "如果是第一次运行": "初めて実行する場合", "第一组插件": "最初のプラグイングループ", "其中$E$是能量": "ここで$E$はエネルギーです", "在结束时": "終了時に", "OpenAI拒绝了请求": "OpenAIはリクエストを拒否しました", "则会在溢出时暴力截断": "オーバーフロー時に強制的に切り捨てられます", "中途接收可能的终止指令": "途中で可能な終了命令を受信する", "experiment等": "実験など", "结束": "終了する", "发送请求到子进程": "子プロセスにリクエストを送信する", "代码已经更新": "コードはすでに更新されています", "情况会好转": "状況は改善されます", "请削减单次输入的文本量": "一度に入力するテキスト量を減らしてください", "每个线程都要“喂狗”": "各スレッドは「犬に餌を与える」必要があります", "也可以写": "書くこともできます", "导入软件依赖失败": "ソフトウェアの依存関係のインポートに失敗しました", "代理网络的地址": "プロキシネットワークのアドレス", "gpt_replying_buffer也写完了": "gpt_replying_bufferも書き終わりました", "依赖检测通过": "Dependency check passed", "并提供改进建议": "And provide improvement suggestions", "Call ChatGLM fail 不能正常加载ChatGLM的参数": "Call ChatGLM fail, unable to load ChatGLM parameters", "请对下面的文章片段做一个概述": "Please summarize the following article fragment", "建议使用docker环境！": "It is recommended to use a docker environment!", "单线": "Single line", "将中文句号": "Replace Chinese period", "高级实验性功能模块调用": "Advanced experimental function module call", "个": "pieces", "MOSS响应异常": "MOSS response exception", "一键更新协议": "One-click update agreement", "最多收纳多少个网页的结果": "Maximum number of web page results to be included", "历史上的今天": "Today in history", "jittorllms尚未加载": "jittorllms has not been loaded", "不输入文件名": "Do not enter file name", "准备文件的下载": "Preparing for file download", "找不到任何golang文件": "Cannot find any golang files", "找不到任何rust文件": "Cannot find any rust files", "写入文件": "Write to file", "LLM_MODEL 格式不正确！": "LLM_MODEL format is incorrect!", "引用次数是链接中的文本": "The reference count is the text in the link", "则使用当前时间生成文件名": "Then use the current time to generate the file name", "第二组插件": "Second set of plugins", "-1代表随机端口": "-1 represents a random port", "无代理状态下很可能无法访问OpenAI家族的模型": "It is very likely that you cannot access the OpenAI family of models without a proxy", "分别为 __enter__": "They are __enter__ respectively", "设定一个最小段落长度阈值": "Set a minimum paragraph length threshold", "批量TranslateFromChiToEngInMarkdown": "Batch TranslateFromChiToEngInMarkdown", "您若希望分享新的功能模组": "If you want to share new functional modules", "先输入问题": "Enter the question first", "理解PDF论文内容": "Understand the content of the PDF paper", "质能方程可以写成$$E=mc^2$$": "The mass-energy equation can be written as $$E=mc^2$$", "安装ChatGLM的依赖": "Install dependencies for ChatGLM", "自动更新程序": "Automatic update program", "备份一个文件": "Backup a file", "并行任务数量限制": "Parallel task quantity limit", "将y中最后一项的输入部分段落化": "Paragraphize the input part of the last item in y", "和": "and", "尝试Prompt": "Try Prompt", "且没有代码段": "And there is no code segment", "设置gradio的并行线程数": "Set the parallel thread number of gradio", "请提取": "Please extract", "向chatbot中添加错误信息": "Add error message to chatbot", "处理文件的上传": "Handle file upload", "异常": "Exception", "此处不修改": "Do not modify here", "*** API_KEY 导入成功": "*** API_KEY imported successfully", "多线程方法": "Multi-threaded method", "也可以根据之前的内容长度来判断段落是否已经足够长": "You can also judge whether the paragraph is long enough based on the length of the previous content", "同样支持多线程": "Also supports multi-threading", "代理所在地": "Location of the proxy", "chatbot 为WebUI中显示的对话列表": "Chatbot is the list of conversations displayed in WebUI", "对话窗的高度": "Height of the conversation window", "体验gpt-4可以试试api2d": "You can try api2d to experience gpt-4", "观察窗": "Observation window", "Latex项目全文英译中": "Full translation of Latex project from English to Chinese", "接下来请将以下代码中包含的所有中文转化为英文": "Next, please translate all the Chinese in the following code into English", "以上材料已经被写入": "以上の材料が書き込まれました", "清理规则包括": "クリーニングルールには以下が含まれます", "展示分割效果": "分割効果を表示する", "运行方法 python crazy_functions/crazy_functions_test.py": "python crazy_functions/crazy_functions_test.pyを実行する方法", "不要遗漏括号": "括弧を省略しないでください", "对IPynb文件进行解析": "IPynbファイルを解析する", "它们会继续向下调用更底层的LLM模型": "それらはより低レベルのLLMモデルを呼び出し続けます", "这个函数用于分割pdf": "この関数はPDFを分割するために使用されます", "等待输入": "入力を待っています", "句号": "句点", "引入一个有cookie的chatbot": "cookieを持つchatbotを導入する", "优先": "優先", "没有提供高级参数功能说明": "高度なパラメータ機能の説明が提供されていません", "找不到任何文件": "ファイルが見つかりません", "将要忽略匹配的文件后缀": "一致するファイルの拡張子を無視する予定です", "函数插件-固定按钮区": "関数プラグイン-固定ボタンエリア", "如果要使用Newbing": "Newbingを使用する場合", "缺少jittorllms的依赖": "jittorllmsの依存関係が不足しています", "尽量是完整的一个section": "可能な限り完全なセクションであること", "请从中提取出“标题”、“收录会议或期刊”、“作者”、“摘要”、“编号”、“作者邮箱”这六个部分": "「タイトル」、「収録会議またはジャーナル」、「著者」、「要約」、「番号」、「著者の電子メール」の6つの部分を抽出してください", "检查USE_PROXY选项是否修改": "USE_PROXYオプションが変更されているかどうかを確認してください", "自动截断": "自動切断", "多线程操作已经开始": "マルチスレッド操作が開始されました", "根据当前的模型类别": "現在のモデルタイプに基づいて", "兼容旧版的配置": "古いバージョンの構成と互換性があります", "找不到任何python文件": "Pythonファイルが見つかりません", "这个bug没找到触发条件": "このバグのトリガー条件が見つかりませんでした", "学术中英互译": "学術的な英中翻訳", "列表递归接龙": "リストの再帰的な接続", "新版本": "新しいバージョン", "返回的结果是": "返された結果は", "以免输入溢出": "オーバーフローを防ぐために", "流式获取输出": "ストリームで出力を取得する", "逐个文件分析": "ファイルを1つずつ分析する", "随机负载均衡": "ランダムな負荷分散", "高级参数输入区": "高度なパラメータ入力エリア", "稍微留一点余地": "少し余裕を持たせる", "并显示到聊天当中": "チャットに表示される", "不在arxiv中无法获取完整摘要": "arxivにないと完全な要約を取得できません", "用户反馈": "ユーザーフィードバック", "有线程锁": "スレッドロックあり", "一键DownloadArxivPapersAndTranslateAbstract": "一括でArxiv論文をダウンロードして要約を翻訳する", "现在您点击任意“红颜色”标识的函数插件时": "今、あなたが任意の「赤い」関数プラグインをクリックすると", "请从": "からお願いします", "也支持同时填写多个api-key": "複数のAPIキーを同時に入力することもできます", "也许等待十几秒后": "おそらく10秒以上待つ必要があります", "第": "第", "在函数插件中被调用": "関数プラグインで呼び出されます", "此外我们也提供可同步处理大量文件的多线程Demo供您参考": "また、大量のファイルを同期的に処理するためのマルチスレッドデモも提供しています", "的配置": "の設定", "数据流的第一帧不携带content": "データストリームの最初のフレームにはcontentが含まれていません", "老旧的Demo": "古いデモ", "预处理一波": "事前処理を行う", "获取所有文章的标题和作者": "すべての記事のタイトルと著者を取得する", "输出 Returns": "Returnsを出力する", "Reduce the length. 本次输入过长": "長さを短くしてください。入力が長すぎます", "抽取摘要": "要約を抽出する", "从最长的条目开始裁剪": "最長のエントリからトリミングを開始する", "2. 替换跨行的连词": "2. 行をまたいだ接続詞を置換する", "并且对于网络上的文件": "そして、ネットワーク上のファイルに対して", "本地文件预览": "ローカルファイルのプレビュー", "手动指定询问哪些模型": "手動でどのモデルを問い合わせるか指定する", "如果有的话": "ある場合は", "直接退出": "直接退出する", "请提交新问题": "新しい問題を提出してください", "您正在调用一个": "あなたは呼び出しています", "请编辑以下文本": "以下のテキストを編集してください", "常见协议无非socks5h/http": "一般的なプロトコルはsocks5h/http以外ありません", "Latex英文纠错": "LatexEnglishErrorCorrection", "连接bing搜索回答问题": "ConnectBingSearchAnswerQuestion", "联网的ChatGPT_bing版": "OnlineChatGPT_BingVersion", "总结音视频": "SummarizeAudioVideo", "动画生成": "GenerateAnimation", "数学动画生成manim": "GenerateMathematicalAnimationManim", "Markdown翻译指定语言": "TranslateMarkdownSpecifiedLanguage", "知识库问答": "KnowledgeBaseQuestionAnswer", "Langchain知识库": "LangchainKnowledgeBase", "读取知识库作答": "ReadKnowledgeBaseAnswer", "交互功能模板函数": "InteractiveFunctionTemplateFunction", "交互功能函数模板": "InteractiveFunctionFunctionTemplate", "Latex英文纠错加PDF对比": "LatexEnglishErrorCorrectionWithPDFComparison", "Latex_Function": "LatexOutputPDFResult", "Latex翻译中文并重新编译PDF": "TranslateChineseAndRecompilePDF", "语音助手": "VoiceAssistant", "微调数据集生成": "FineTuneDatasetGeneration", "chatglm微调工具": "ChatGLMFineTuningTool", "启动微调": "StartFineTuning", "sprint亮靛": "SprintAzureIndigo", "专业词汇声明": "ProfessionalVocabularyDeclaration", "Latex精细分解与转化": "LatexDetailedDecompositionAndConversion", "编译Latex": "CompileLatex", "将代码转为动画": "コードをアニメーションに変換する", "解析arxiv网址失败": "arxivのURLの解析に失敗しました", "其他模型转化效果未知": "他のモデルの変換効果は不明です", "把文件复制过去": "ファイルをコピーする", "！！！如果需要运行量化版本": "！！！量子化バージョンを実行する必要がある場合", "报错信息如下. 如果是与网络相关的问题": "エラーメッセージは次のとおりです。ネットワークに関連する問題の場合", "请检查ALIYUN_TOKEN和ALIYUN_APPKEY是否过期": "ALIYUN_TOKENとALIYUN_APPKEYの有効期限を確認してください", "编译结束": "コンパイル終了", "只读": "読み取り専用", "模型选择是": "モデルの選択は", "正在从github下载资源": "GitHubからリソースをダウンロードしています", "同时分解长句": "同時に長い文を分解する", "寻找主tex文件": "メインのtexファイルを検索する", "例如您可以将以下命令复制到下方": "たとえば、以下のコマンドを下にコピーできます", "使用中文总结音频“": "中国語で音声を要約する", "此处填API密钥": "ここにAPIキーを入力してください", "裁剪输入": "入力をトリミングする", "当前语言模型温度设定": "現在の言語モデルの温度設定", "history 是之前的对话列表": "historyは以前の対話リストです", "对输入的word文档进行摘要生成": "入力されたWord文書の要約を生成する", "输入问题后点击该插件": "質問を入力した後、このプラグインをクリックします", "仅在Windows系统进行了测试": "Windowsシステムでのみテストされています", "reverse 操作必须放在最后": "reverse操作は最後に配置する必要があります", "即将编译PDF": "PDFをコンパイルする予定です", "执行错误": "エラーが発生しました", "段音频完成了吗": "セグメントのオーディオは完了しましたか", "然后重启程序": "それからプログラムを再起動してください", "是所有LLM的通用接口": "これはすべてのLLMの共通インターフェースです", "当前报错的latex代码处于第": "現在のエラーのあるLaTeXコードは第", "🏃‍♂️🏃‍♂️🏃‍♂️ 子进程执行": "🏃‍♂️🏃‍♂️🏃‍♂️ サブプロセスの実行", "用来描述你的要求": "要求を説明するために使用されます", "原始PDF编译是否成功": "元のPDFのコンパイルは成功しましたか", "本地Latex论文精细翻译": "ローカルのLaTeX論文の詳細な翻訳", "设置OpenAI密钥和模型": "OpenAIキーとモデルの設定", "如果使用ChatGLM2微调模型": "ChatGLM2ファインチューニングモデルを使用する場合", "项目Github地址 \\url{https": "プロジェクトのGithubアドレス \\url{https", "将前后断行符脱离": "前後の改行文字を削除します", "该项目的Latex主文件是": "このプロジェクトのLaTeXメインファイルは", "编译已经开始": "コンパイルが開始されました", "*{\\scriptsize\\textbf{警告": "*{\\scriptsize\\textbf{警告", "从一批文件": "一連のファイルから", "等待用户的再次调用": "ユーザーの再呼び出しを待っています", "目前仅支持GPT3.5/GPT4": "現在、GPT3.5/GPT4のみをサポートしています", "如果一句话小于7个字": "1つの文が7文字未満の場合", "目前对机器学习类文献转化效果最好": "現在、機械学習の文献変換効果が最も良いです", "寻找主文件": "メインファイルを検索中", "解除插件状态": "プラグインの状態を解除します", "默认为Chinese": "デフォルトはChineseです", "依赖不足": "不足の依存関係", "编译文献交叉引用": "文献の相互参照をコンパイルする", "对不同latex源文件扣分": "異なるLaTeXソースファイルに罰則を課す", "再列出用户可能提出的三个问题": "ユーザーが提出する可能性のある3つの問題を再リスト化する", "建议排查": "トラブルシューティングの提案", "生成时间戳": "タイムスタンプの生成", "检查config中的AVAIL_LLM_MODELS选项": "configのAVAIL_LLM_MODELSオプションを確認する", "chatglmft 没有 sys_prompt 接口": "chatglmftにはsys_promptインターフェースがありません", "在一个异步线程中采集音频": "非同期スレッドでオーディオを収集する", "初始化插件状态": "プラグインの状態を初期化する", "内含已经翻译的Tex文档": "翻訳済みのTexドキュメントが含まれています", "请注意自我隐私保护哦！": "プライバシー保護に注意してください！", "使用正则表达式查找半行注释": "正規表現を使用して半行コメントを検索する", "不能正常加载ChatGLMFT的参数！": "ChatGLMFTのパラメータを正常にロードできません！", "首先你在中文语境下通读整篇论文": "まず、中国語の文脈で論文全体を読んでください", "如 绿帽子*深蓝色衬衫*黑色运动裤": "例えば、緑の帽子*濃い青のシャツ*黒のスポーツパンツ", "默认为default": "デフォルトはdefaultです", "将": "置き換える", "使用 Unsplash API": "Unsplash APIを使用する", "会被加在你的输入之前": "あなたの入力の前に追加されます", "还需要填写组织": "組織を入力する必要があります", "test_LangchainKnowledgeBase读取": "test_LangchainKnowledgeBaseの読み込み", "目前不支持历史消息查询": "現在、過去のメッセージのクエリはサポートされていません", "临时存储用于调试": "デバッグ用の一時的なストレージ", "提取总结": "テキストの翻訳", "每秒采样数量": "テキストの翻訳", "但通常不会出现在正文": "テキストの翻訳", "通过调用conversations_open方法打开一个频道": "テキストの翻訳", "导致输出不完整": "テキストの翻訳", "获取已打开频道的最新消息并返回消息列表": "テキストの翻訳", "Tex源文件缺失！": "テキストの翻訳", "如果需要使用Slack Claude": "テキストの翻訳", "扭转的范围": "テキストの翻訳", "使用latexdiff生成论文转化前后对比": "テキストの翻訳", "--读取文件": "テキストの翻訳", "调用openai api 使用whisper-1模型": "テキストの翻訳", "避免遗忘导致死锁": "テキストの翻訳", "在多Tex文档中": "テキストの翻訳", "失败时": "テキストの翻訳", "然后转移到指定的另一个路径中": "テキストの翻訳", "使用Newbing": "テキストの翻訳", "的参数": "テキストの翻訳", "后者是OPENAI的结束条件": "テキストの翻訳", "构建知识库": "テキストの翻訳", "吸收匿名公式": "テキストの翻訳", "前缀": "テキストの翻訳", "会直接转到该函数": "テキストの翻訳", "Claude失败": "テキストの翻訳", "P.S. 但愿没人把latex模板放在里面传进来": "P.S. 但愿没人把latex模板放在里面传进来", "临时地启动代理网络": "临时地启动代理网络", "读取文件内容到内存": "読み込んだファイルの内容をメモリに保存する", "总结音频": "音声をまとめる", "没有找到任何可读取文件": "読み込み可能なファイルが見つかりません", "获取Slack消息失败": "Slackメッセージの取得に失敗しました", "用黑色标注转换区": "黒い注釈で変換エリアをマークする", "此插件处于开发阶段": "このプラグインは開発中です", "其他操作系统表现未知": "他のオペレーティングシステムの動作は不明です", "返回找到的第一个": "最初に見つかったものを返す", "发现已经存在翻译好的PDF文档": "翻訳済みのPDFドキュメントが既に存在することがわかりました", "不包含任何可用于": "使用できるものは含まれていません", "发送到openai音频解析终端": "openai音声解析端に送信する", "========================================= 插件主程序2 =====================================================": "========================================= プラグインメインプログラム2 =====================================================", "正在重试": "再試行中", "从而更全面地理解项目的整体功能": "プロジェクトの全体的な機能をより理解するために", "正在等您说完问题": "質問が完了するのをお待ちしています", "使用教程详情见 request_llms/README.md": "使用方法の詳細については、request_llms/README.mdを参照してください", "6.25 加入判定latex模板的代码": "6.25 テンプレートの判定コードを追加", "找不到任何音频或视频文件": "音声またはビデオファイルが見つかりません", "请求GPT模型的": "GPTモデルのリクエスト", "行": "行", "分析上述回答": "上記の回答を分析する", "如果要使用ChatGLMFT": "ChatGLMFTを使用する場合", "上传Latex项目": "Latexプロジェクトをアップロードする", "如参考文献、脚注、图注等": "参考文献、脚注、図のキャプションなど", "未配置": "設定されていません", "请在此处给出自定义翻译命令": "カスタム翻訳コマンドをここに入力してください", "第二部分": "第2部分", "解压失败! 需要安装pip install py7zr来解压7z文件": "解凍に失敗しました！7zファイルを解凍するにはpip install py7zrをインストールする必要があります", "吸收在42行以内的begin-end组合": "42行以内のbegin-endの組み合わせを取り込む", "Latex文件融合完成": "Latexファイルの統合が完了しました", "输出html调试文件": "HTMLデバッグファイルの出力", "论文概况": "論文の概要", "修复括号": "括弧の修復", "赋予插件状态": "プラグインの状態を付与する", "标注节点的行数范围": "ノードの行数範囲を注釈する", "MOSS can understand and communicate fluently in the language chosen by the user such as English and 中文. MOSS can perform any language-based tasks.": "MOSSは、ユーザーが選択した言語（英語や中文など）でスムーズに理解し、コミュニケーションすることができます。MOSSは、言語に基づくさまざまなタスクを実行できます。", "LLM_MODEL是默认选中的模型": "LLM_MODELはデフォルトで選択されたモデルです", "配合前缀可以把你的输入内容用引号圈起来": "接頭辞と組み合わせて、入力内容を引用符で囲むことができます", "获取关键词": "キーワードの取得", "本项目现已支持OpenAI和Azure的api-key": "このプロジェクトは、OpenAIおよびAzureのAPIキーをサポートしています", "欢迎使用 MOSS 人工智能助手！": "MOSS AIアシスタントをご利用いただきありがとうございます！", "在执行完成之后": "実行が完了した後", "正在听您讲话": "お話をお聞きしています", "Claude回复的片段": "Claudeの返信の一部", "返回": "戻る", "期望格式例如": "期待される形式の例", "gpt 多线程请求": "GPTマルチスレッドリクエスト", "当前工作路径为": "現在の作業パスは", "该PDF由GPT-Academic开源项目调用大语言模型+Latex翻译插件一键生成": "このPDFはGPT-Academicオープンソースプロジェクトによって大規模言語モデル+Latex翻訳プラグインを使用して一括生成されました", "解决插件锁定时的界面显示问题": "プラグインのロック時のインターフェース表示の問題を解決する", "默认 secondary": "デフォルトのセカンダリ", "会把列表拆解": "リストを分解します", "暂时不支持历史消息": "一時的に歴史メッセージはサポートされていません", "或者重启之后再度尝试": "または再起動後に再試行してください", "吸收其他杂项": "他の雑項を吸収する", "双手离开鼠标键盘吧": "両手をマウスとキーボードから離してください", "建议更换代理协议": "プロキシプロトコルの変更をお勧めします", "音频助手": "オーディオアシスタント", "请耐心等待": "お待ちください", "翻译结果": "翻訳結果", "请在此处追加更细致的矫错指令": "ここにより詳細なエラー修正命令を追加してください", "编译原始PDF": "元のPDFをコンパイルする", "-构建知识库": "-ナレッジベースの構築", "删除中间文件夹": "中間フォルダを削除する", "这段代码定义了一个名为TempProxy的空上下文管理器": "このコードはTempProxyという名前の空のコンテキストマネージャを定義しています", "参数说明": "パラメータの説明", "正在预热文本向量化模组": "テキストベクトル化モジュールのプリヒート中", "函数插件": "関数プラグイン", "右下角更换模型菜单中可切换openai": "右下のモデルメニューでopenaiを切り替えることができます", "先上传数据集": "まずデータセットをアップロードしてください", "LatexEnglishErrorCorrection+高亮修正位置": "テキストの翻訳", "正在构建知识库": "テキストの翻訳", "用红色标注处保留区": "テキストの翻訳", "安装Claude的依赖": "テキストの翻訳", "已禁用": "テキストの翻訳", "是否在提交时自动清空输入框": "テキストの翻訳", "GPT 学术优化": "テキストの翻訳", "需要特殊依赖": "テキストの翻訳", "test_联网回答问题": "テキストの翻訳", "除非您是论文的原作者": "テキストの翻訳", "即可见": "テキストの翻訳", "解析为简体中文": "テキストの翻訳", "解析整个Python项目": "テキストの翻訳", "========================================= 插件主程序1 =====================================================": "テキストの翻訳", "当前参数": "テキストの翻訳", "处理个别特殊插件的锁定状态": "テキストの翻訳", "已知某些代码的局部作用是": "テキストの翻訳", "请务必用 pip install -r requirements.txt 指令安装依赖": "テキストの翻訳", "安装": "テキストの翻訳", "请登录OpenAI查看详情 https": "テキストの翻訳", "必须包含documentclass": "テキストの翻訳", "极少数情况下": "テキストの翻訳", "并将返回的频道ID保存在属性CHANNEL_ID中": "テキストの翻訳", "您的 API_KEY 不满足任何一种已知的密钥格式": "テキストの翻訳", "-预热文本向量化模组": "テキストの翻訳", "什么都没有": "テキストの翻訳", "等待GPT响应": "テキストの翻訳", "请尝试把以下指令复制到高级参数区": "テキストの翻訳", "模型参数": "テキストの翻訳", "先删除": "テキストの翻訳", "响应中": "テキストの翻訳", "开始接收chatglmft的回复": "テキストの翻訳", "手动指定语言": "テキストの翻訳", "获取线程锁": "テキストの翻訳", "当前大语言模型": "テキストの翻訳", "段音频的第": "テキストの翻訳", "正在编译对比PDF": "テキストの翻訳", "根据需要切换prompt": "テキストの翻訳", "取评分最高者返回": "テキストの翻訳", "如果您是论文原作者": "テキストの翻訳", "段音频的主要内容": "テキストの翻訳", "为啥chatgpt会把cite里面的逗号换成中文逗号呀": "テキストの翻訳", "为每一位访问的用户赋予一个独一无二的uuid编码": "テキストの翻訳", "将每次对话记录写入Markdown格式的文件中": "テキストの翻訳", "ChatGLMFT尚未加载": "テキストの翻訳", "切割音频文件": "テキストの翻訳", "例如 f37f30e0f9934c34a992f6f64f7eba4f": "テキストの翻訳", "work_folder = Latex预处理": "テキストの翻訳", "出问题了": "問題が発生しました", "等待Claude响应中": "Claudeの応答を待っています", "增强稳健性": "信頼性を向上させる", "赋予插件锁定 锁定插件回调路径": "プラグインにコールバックパスをロックする", "将多文件tex工程融合为一个巨型tex": "複数のファイルのtexプロジェクトを1つの巨大なtexに統合する", "参考文献转Bib": "参考文献をBibに変換する", "由于提问含不合规内容被Azure过滤": "質問が規則に違反しているため、Azureによってフィルタリングされました", "读取优先级": "優先度を読み取る", "格式如org-xxxxxxxxxxxxxxxxxxxxxxxx": "形式はorg-xxxxxxxxxxxxxxxxxxxxxxxxのようです", "辅助gpt生成代码": "GPTのコード生成を補助する", "读取音频文件": "音声ファイルを読み取る", "输入arxivID": "arxivIDを入力する", "转化PDF编译是否成功": "PDFのコンパイルが成功したかどうかを変換する", "Call ChatGLMFT fail 不能正常加载ChatGLMFT的参数": "ChatGLMFTのパラメータを正常にロードできませんでした", "创建AcsClient实例": "AcsClientのインスタンスを作成する", "将 chatglm 直接对齐到 chatglm2": "chatglmをchatglm2に直接整列させる", "要求": "要求", "子任务失败时的重试次数": "サブタスクが失敗した場合のリトライ回数", "请求子进程": "サブプロセスを要求する", "按钮是否可见": "ボタンが表示可能かどうか", "将 \\include 命令转换为 \\input 命令": "\\includeコマンドを\\inputコマンドに変換する", "用户填3": "ユーザーが3を入力する", "后面是英文逗号": "後ろに英語のカンマがあります", "吸收iffalse注释": "iffalseコメントを吸収する", "请稍候": "お待ちください", "摘要生成后的文档路径": "要約生成後のドキュメントのパス", "主程序即将开始": "メインプログラムがすぐに開始されます", "处理历史信息": "履歴情報の処理", "根据给定的切割时长将音频文件切割成多个片段": "指定された分割時間に基づいてオーディオファイルを複数のセグメントに分割する", "解决部分词汇翻译不准确的问题": "一部の用語の翻訳の不正確さを解決する", "即将退出": "すぐに終了します", "用于给一小段代码上代理": "一部のコードにプロキシを適用するために使用されます", "提取文件扩展名": "ファイルの拡張子を抽出する", "目前支持的格式": "現在サポートされている形式", "第一次调用": "最初の呼び出し", "异步方法": "非同期メソッド", "P.S. 顺便把Latex的注释去除": "P.S. LaTeXのコメントを削除する", "构建完成": "ビルドが完了しました", "缺少": "不足しています", "建议暂时不要使用": "一時的に使用しないことをお勧めします", "对比PDF编译是否成功": "PDFのコンパイルが成功したかどうかを比較する", "填入azure openai api的密钥": "Azure OpenAI APIのキーを入力してください", "功能尚不稳定": "機能はまだ安定していません", "则跳过GPT请求环节": "GPTリクエストのスキップ", "即不处理之前的对话历史": "以前の対話履歴を処理しない", "非Openai官方接口返回了错误": "非公式のOpenAI APIがエラーを返しました", "其他类型文献转化效果未知": "他のタイプの文献の変換効果は不明です", "给出一些判定模板文档的词作为扣分项": "テンプレートドキュメントの単語を減点項目として提供する", "找 API_ORG 设置项": "API_ORGの設定項目を検索します", "调用函数": "関数を呼び出します", "需要手动安装新增的依赖库": "新しい依存ライブラリを手動でインストールする必要があります", "或者使用此插件继续上传更多文件": "または、このプラグインを使用してさらにファイルをアップロードします", "640个字节为一组": "640バイトごとにグループ化します", "逆转出错的段落": "エラーのあるパラグラフを逆転させます", "对话助手函数插件": "対話アシスタント関数プラグイン", "前者是API2D的结束条件": "前者はAPI2Dの終了条件です", "终端": "ターミナル", "仅调试": "デバッグのみ", "论文": "論文", "想象一个穿着者": "着用者を想像してください", "音频内容是": "音声の内容は", "如果需要使用AZURE 详情请见额外文档 docs\\use_azure.md": "AZUREを使用する必要がある場合は、詳細については別のドキュメント docs\\use_azure.md を参照してください", "请先将.doc文档转换为.docx文档": ".docドキュメントを.docxドキュメントに変換してください", "请查看终端的输出或耐心等待": "ターミナルの出力を確認するか、お待ちください", "初始化音频采集线程": "オーディオキャプチャスレッドを初期化します", "用该压缩包+ConversationHistoryArchive进行反馈": "この圧縮ファイル+ConversationHistoryArchiveを使用してフィードバックします", "阿里云实时语音识别 配置难度较高 仅建议高手用户使用 参考 https": "阿里云リアルタイム音声認識の設定は難しいため、上級ユーザーのみに推奨されます 参考 https", "多线程翻译开始": "マルチスレッド翻訳が開始されました", "只有GenerateImage和生成图像相关": "GenerateImageと関連する画像の生成のみ", "代理数据解析失败": "プロキシデータの解析に失敗しました", "建议使用英文单词": "英単語の使用をお勧めします", "功能描述": "機能の説明", "读 docs\\use_azure.md": "ドキュメントを読む", "将消耗较长时间下载中文向量化模型": "中国語のベクトル化モデルをダウンロードするのに時間がかかります", "表示频道ID": "チャネルIDを表示する", "未知指令": "不明なコマンド", "包含documentclass关键字": "documentclassキーワードを含む", "中读取数据构建知识库": "データを読み取って知識ベースを構築する", "远程云服务器部署": "リモートクラウドサーバーにデプロイする", "输入部分太自由": "入力が自由すぎる", "读取pdf文件": "PDFファイルを読み込む", "将两个PDF拼接": "2つのPDFを結合する", "默认值为1000": "デフォルト値は1000です", "写出文件": "ファイルに書き出す", "生成的视频文件路径": "生成されたビデオファイルのパス", "ArXiv论文精细翻译": "ArXiv論文の詳細な翻訳", "用latex编译为PDF对修正处做高亮": "LaTeXでコンパイルしてPDFに修正をハイライトする", "点击“停止”键可终止程序": "「停止」ボタンをクリックしてプログラムを終了できます", "否则将导致每个人的Claude问询历史互相渗透": "さもないと、各人のClaudeの問い合わせ履歴が相互に侵入します", "音频文件名": "オーディオファイル名", "的参数！": "のパラメータ！", "对话历史": "対話履歴", "当下一次用户提交时": "次のユーザーの提出時に", "数学GenerateAnimation": "数学GenerateAnimation", "如果要使用Claude": "Claudeを使用する場合は", "请向下翻": "下にスクロールしてください", "报告已经添加到右侧“文件上传区”": "報告は右側の「ファイルアップロードエリア」に追加されました", "删除整行的空注释": "空のコメントを含む行を削除する", "建议直接在API_KEY处填写": "API_KEYの場所に直接入力することをお勧めします", "暗色模式 / 亮色模式": "ダークモード/ライトモード", "做一些外观色彩上的调整": "外観の色調整を行う", "请切换至“KnowledgeBaseQuestionAnswer”插件进行知识库访问": "ナレッジベースのアクセスには「KnowledgeBaseQuestionAnswer」プラグインに切り替えてください", "它*必须*被包含在AVAIL_LLM_MODELS列表中": "それはAVAIL_LLM_MODELSリストに含まれている必要があります", "并设置参数": "パラメータを設定する", "待处理的word文档路径": "処理待ちのWord文書のパス", "调用缓存": "キャッシュを呼び出す", "片段": "フラグメント", "否则结束循环": "それ以外の場合はループを終了する", "请对下面的音频片段做概述": "以下のオーディオフラグメントについて概要を作成してください", "高危设置! 常规情况下不要修改! 通过修改此设置": "高リスクの設定！通常は変更しないでください！この設定を変更することで", "插件锁定中": "プラグインがロックされています", "开始": "開始", "但请查收结果": "結果を確認してください", "刷新Gradio前端界面": "Gradioフロントエンドインターフェースをリフレッシュする", "批量SummarizeAudioVideo": "オーディオビデオを一括要約する", "一个单实例装饰器": "単一のインスタンスデコレータ", "Claude响应异常": "Claudeの応答が異常です", "但内部用stream的方法避免中途网线被掐": "ただし、途中でネットワーク接続が切断されることを避けるために、内部ではストリームを使用しています", "检查USE_PROXY": "USE_PROXYを確認する", "永远给定None": "常にNoneを指定する", "报告如何远程获取": "報告のリモート取得方法", "您可以到Github Issue区": "GithubのIssueエリアにアクセスできます", "如果只询问1个大语言模型": "1つの大規模言語モデルにのみ質問する場合", "为了防止大语言模型的意外谬误产生扩散影响": "大規模言語モデルの誤った結果が広がるのを防ぐために", "编译BibTex": "BibTexのコンパイル", "⭐多线程方法": "マルチスレッドの方法", "推荐http": "httpをおすすめします", "如果要使用": "使用する場合", "的单词": "の単語", "如果本地使用不建议加这个": "ローカルで使用する場合はお勧めしません", "避免线程阻塞": "スレッドのブロックを回避する", "吸收title与作者以上的部分": "タイトルと著者以上の部分を吸収する", "作者": "著者", "5刀": "5ドル", "ChatGLMFT响应异常": "ChatGLMFTの応答異常", "才能继续下面的步骤": "次の手順に進むために", "对这个人外貌、身处的环境、内心世界、过去经历进行描写": "この人の外見、環境、内面世界、過去の経験について描写する", "找不到微调模型检查点": "ファインチューニングモデルのチェックポイントが見つかりません", "请仔细鉴别并以原文为准": "注意深く確認し、元のテキストを参照してください", "计算文件总时长和切割点": "ファイルの総時間とカットポイントを計算する", "我将为您查找相关壁纸": "関連する壁紙を検索します", "此插件Windows支持最佳": "このプラグインはWindowsに最適です", "请输入关键词": "キーワードを入力してください", "以下所有配置也都支持利用环境变量覆写": "以下のすべての設定は環境変数を使用して上書きすることもサポートしています", "尝试第": "第#", "开始生成动画": "アニメーションの生成を開始します", "免费": "無料", "我好！": "私は元気です！", "str类型": "strタイプ", "生成数学动画": "数学アニメーションの生成", "GPT结果已输出": "GPTの結果が出力されました", "PDF文件所在的路径": "PDFファイルのパス", "源码自译解": "ソースコードの自動翻訳解析", "格式如org-123456789abcdefghijklmno的": "org-123456789abcdefghijklmnoの形式", "请对这部分内容进行语法矫正": "この部分の内容に文法修正を行ってください", "调用whisper模型音频转文字": "whisperモデルを使用して音声をテキストに変換する", "编译转化后的PDF": "変換されたPDFをコンパイルする", "将音频解析为简体中文": "音声を簡体字中国語に解析する", "删除或修改歧义文件": "曖昧なファイルを削除または修正する", "ChatGLMFT消耗大量的内存": "ChatGLMFTは大量のメモリを消費します", "图像生成所用到的提示文本": "画像生成に使用されるヒントテキスト", "如果已经存在": "既に存在する場合", "以下是一篇学术论文的基础信息": "以下は学術論文の基本情報です", "解压失败! 需要安装pip install rarfile来解压rar文件": "解凍に失敗しました！rarファイルを解凍するにはpip install rarfileをインストールする必要があります", "一般是文本过长": "通常、テキストが長すぎます", "单线程": "シングルスレッド", "Linux下必须使用Docker安装": "LinuxではDockerを使用してインストールする必要があります", "请先上传文件素材": "まずファイル素材をアップロードしてください", "如果分析错误": "もし解析エラーがある場合", "快捷的调试函数": "便利なデバッグ関数", "欢迎使用 MOSS 人工智能助手！输入内容即可进行对话": "MOSS AIアシスタントをご利用いただきありがとうございます！入力内容を入力すると、対話ができます", "json等": "jsonなど", "--读取参数": "--パラメータの読み込み", "⭐单线程方法": "⭐シングルスレッドメソッド", "请用一句话概括这些文件的整体功能": "これらのファイルの全体的な機能を一文で要約してください", "用于灵活调整复杂功能的各种参数": "複雑な機能を柔軟に調整するためのさまざまなパラメータ", "默认 False": "デフォルトはFalseです", "生成中文PDF": "中国語のPDFを生成する", "正在处理": "処理中", "需要被切割的音频文件名": "分割する必要のある音声ファイル名", "根据文本使用GPT模型生成相应的图像": "テキストに基づいてGPTモデルを使用して対応する画像を生成する", "可选": "オプション", "Aliyun音频服务异常": "Aliyunオーディオサービスの異常", "尝试下载": "ダウンロードを試みる", "需Latex": "LaTeXが必要です", "拆分过长的Markdown文件": "長すぎるMarkdownファイルを分割する", "当前支持的格式包括": "現在サポートされている形式には", "=================================== 工具函数 ===============================================": "=================================== ユーティリティ関数 ===============================================", "所有音频都总结完成了吗": "すべてのオーディオが要約されましたか", "没有设置ANTHROPIC_API_KEY": "ANTHROPIC_API_KEYが設定されていません", "详见项目主README.md": "詳細はプロジェクトのメインREADME.mdを参照してください", "使用": "使用する", "P.S. 其他可用的模型还包括": "P.S. 其他可用的模型还包括", "保证括号正确": "保证括号正确", "或代理节点": "或代理节点", "整理结果为压缩包": "整理结果为压缩包", "实时音频采集": "实时音频采集", "获取回复": "获取回复", "插件可读取“输入区”文本/路径作为参数": "插件可读取“输入区”文本/路径作为参数", "请讲话": "请讲话", "将文件复制一份到下载区": "将文件复制一份到下载区", "from crazy_functions.虚空终端 import 终端": "from crazy_functions.虚空终端 import 终端", "这个paper有个input命令文件名大小写错误！": "这个paper有个input命令文件名大小写错误！", "解除插件锁定": "解除插件锁定", "不能加载Claude组件": "不能加载Claude组件", "如果有必要": "如果有必要", "禁止移除或修改此警告": "禁止移除或修改此警告", "然后进行问答": "然后进行问答", "响应异常": "响应异常", "使用英文": "使用英文", "add gpt task 创建子线程请求gpt": "add gpt task 创建子线程请求gpt", "实际得到格式": "实际得到格式", "请继续分析其他源代码": "请继续分析其他源代码", "”的主要内容": "”的主要内容", "防止proxies单独起作用": "防止proxies单独起作用", "临时地激活代理网络": "临时地激活代理网络", "屏蔽空行和太短的句子": "屏蔽空行和太短的句子", "把某个路径下所有文件压缩": "把某个路径下所有文件压缩", "您需要首先调用构建知识库": "您需要首先调用构建知识库", "翻译-": "翻译-", "Newbing 请求失败": "Newbing 请求失败", "次编译": "次编译", "后缀": "后缀", "文本碎片重组为完整的tex片段": "文本碎片重组为完整的tex片段", "待注入的知识库名称id": "待注入的知识库名称id", "消耗时间的函数": "消耗时间的函数", "You are associated with a deactivated account. OpenAI以账户失效为由": "You are associated with a deactivated account. OpenAI以账户失效为由", "成功啦": "成功啦", "音频文件的路径": "音频文件的路径", "英文Latex项目全文纠错": "英文Latex项目全文纠错", "将子线程的gpt结果写入chatbot": "将子线程的gpt结果写入chatbot", "开始最终总结": "开始最终总结", "调用": "调用", "正在锁定插件": "正在锁定插件", "记住当前的label": "记住当前的label", "根据自然语言执行插件命令": "根据自然语言执行插件命令", "response中会携带traceback报错信息": "response中会携带traceback报错信息", "避免多用户干扰": "避免多用户干扰", "顺利完成": "顺利完成", "详情见https": "详情见https", "清空label": "ラベルをクリアする", "这需要一段时间计算": "これには時間がかかります", "找不到": "見つかりません", "消耗大量的内存": "大量のメモリを消費する", "安装方法https": "インストール方法https", "为发送请求做准备": "リクエストの準備をする", "第1次尝试": "1回目の試み", "检查结果": "結果をチェックする", "精细切分latex文件": "LaTeXファイルを細かく分割する", "api2d等请求源": "api2dなどのリクエストソース", "填入你亲手写的部署名": "あなたが手書きしたデプロイ名を入力してください", "给出指令": "指示を与える", "请问什么是质子": "プロトンとは何ですか", "请直接去该路径下取回翻译结果": "直接そのパスに移動して翻訳結果を取得してください", "等待Claude回复的片段": "Claudeの返信を待っているフラグメント", "Latex没有安装": "LaTeXがインストールされていません", "文档越长耗时越长": "ドキュメントが長いほど時間がかかります", "没有阿里云语音识别APPKEY和TOKEN": "阿里雲の音声認識のAPPKEYとTOKENがありません", "分析结果": "結果を分析する", "请立即终止程序": "プログラムを即座に終了してください", "正在尝试自动安装": "自動インストールを試みています", "请直接提交即可": "直接提出してください", "将指定目录下的PDF文件从英文翻译成中文": "指定されたディレクトリ内のPDFファイルを英語から中国語に翻訳する", "请查收结果": "結果を確認してください", "上下布局": "上下布局", "此处可以输入解析提示": "此处可以输入解析提示", "前面是中文逗号": "前面是中文逗号", "的依赖": "的依赖", "材料如下": "材料如下", "欢迎加README中的QQ联系开发者": "欢迎加README中的QQ联系开发者", "开始下载": "開始ダウンロード", "100字以内": "100文字以内", "创建request": "リクエストの作成", "创建存储切割音频的文件夹": "切り取られた音声を保存するフォルダの作成", "⭐主进程执行": "⭐メインプロセスの実行", "音频解析结果": "音声解析結果", "Your account is not active. OpenAI以账户失效为由": "アカウントがアクティブではありません。OpenAIはアカウントの無効化を理由にしています", "虽然PDF生成失败了": "PDFの生成に失敗しました", "如果这里报错": "ここでエラーが発生した場合", "前面是中文冒号": "前面は中国語のコロンです", "SummarizeAudioVideo内容": "SummarizeAudioVideoの内容", "openai的官方KEY需要伴随组织编码": "openaiの公式KEYは組織コードと一緒に必要です", "是本次输入": "これは今回の入力です", "色彩主体": "色彩の主体", "Markdown翻译": "Markdownの翻訳", "会被加在你的输入之后": "あなたの入力の後に追加されます", "失败啦": "失敗しました", "每个切割音频片段的时长": "各切り取り音声の長さ", "拆分过长的latex片段": "原始文本", "待提取的知识库名称id": "原始文本", "在这里放一些网上搜集的demo": "原始文本", "环境变量配置格式见docker-compose.yml": "原始文本", "Claude组件初始化成功": "原始文本", "尚未加载": "原始文本", "等待Claude响应": "原始文本", "重组": "原始文本", "将文件添加到chatbot cookie中": "原始文本", "回答完问题后": "原始文本", "将根据报错信息修正tex源文件并重试": "原始文本", "是否在触发时清除历史": "原始文本", "尝试执行Latex指令失败": "原始文本", "默认 True": "原始文本", "文本碎片重组为完整的tex文件": "原始文本", "注意事项": "原始文本", "您接下来不能再使用其他插件了": "原始文本", "属性": "原始文本", "正在编译PDF文档": "原始文本", "提取视频中的音频": "原始文本", "正在同时咨询ChatGPT和ChatGLM……": "原始文本", "Chuanhu-Small-and-Beautiful主题": "原始文本", "版权归原文作者所有": "原始文本", "如果程序停顿5分钟以上": "原始文本", "请输入要翻译成哪种语言": "日本語", "以秒为单位": "秒単位で", "请以以下方式load模型！！！": "以下の方法でモデルをロードしてください！！！", "使用时": "使用時", "对这个人外貌、身处的环境、内心世界、人设进行描写": "この人の外見、環境、内面世界、キャラクターを描写する", "例如翻译、解释代码、润色等等": "例えば翻訳、コードの説明、修正など", "多线程Demo": "マルチスレッドデモ", "不能正常加载": "正常にロードできません", "还原部分原文": "一部の元のテキストを復元する", "可以将自身的状态存储到cookie中": "自身の状態をcookieに保存することができます", "释放线程锁": "スレッドロックを解放する", "当前知识库内的有效文件": "現在のナレッジベース内の有効なファイル", "也是可读的": "読み取り可能です", "等待ChatGLMFT响应中": "ChatGLMFTの応答を待っています", "输入 stop 以终止对话": "stopを入力して対話を終了します", "对整个Latex项目进行纠错": "全体のLatexプロジェクトを修正する", "报错信息": "エラーメッセージ", "下载pdf文件未成功": "PDFファイルのダウンロードに失敗しました", "正在加载Claude组件": "Claudeコンポーネントを読み込んでいます", "格式": "フォーマット", "Claude响应缓慢": "Claudeの応答が遅い", "该选项即将被弃用": "このオプションはまもなく廃止されます", "正常状态": "正常な状態", "中文Bing版": "中国語Bing版", "代理网络配置": "プロキシネットワークの設定", "Openai 限制免费用户每分钟20次请求": "Openaiは無料ユーザーに対して1分間に20回のリクエスト制限を設けています", "gpt写的": "gptで書かれた", "向已打开的频道发送一条文本消息": "既に開いているチャンネルにテキストメッセージを送信する", "缺少ChatGLMFT的依赖": "ChatGLMFTの依存関係が不足しています", "注意目前不能多人同时调用Claude接口": "現在、複数の人が同時にClaudeインターフェースを呼び出すことはできません", "或者不在环境变量PATH中": "または環境変数PATHに存在しません", "提问吧! 但注意": "質問してください！ただし注意してください", "因此选择GenerateImage函数": "したがって、GenerateImage関数を選択します", "无法找到一个主Tex文件": "メインのTexファイルが見つかりません", "转化PDF编译已经成功": "PDF変換コンパイルが成功しました", "因为在同一个频道里存在多人使用时历史消息渗透问题": "同じチャンネルで複数の人が使用する場合、過去のメッセージが漏洩する問題があります", "SlackClient类用于与Slack API进行交互": "SlackClientクラスはSlack APIとのインタラクションに使用されます", "如果存在调试缓存文件": "デバッグキャッシュファイルが存在する場合", "举例": "例を挙げる", "无需填写": "記入する必要はありません", "配置教程&视频教程": "設定チュートリアル＆ビデオチュートリアル", "最后一步处理": "最後のステップの処理", "定位主Latex文件": "メインのLatexファイルを特定する", "暂不提交": "一時的に提出しない", "由于最为关键的转化PDF编译失败": "最も重要なPDF変換コンパイルが失敗したため", "用第二人称": "第二人称を使用する", "例如 RoPlZrM88DnAFkZK": "例えば RoPlZrM88DnAFkZK", "没有设置ANTHROPIC_API_KEY选项": "ANTHROPIC_API_KEYオプションが設定されていません", "找不到任何.tex文件": "テキストの翻訳", "请您不要删除或修改这行警告": "テキストの翻訳", "只有第二步成功": "テキストの翻訳", "调用Claude时": "テキストの翻訳", "输入 clear 以清空对话历史": "テキストの翻訳", "= 2 通过一些Latex模板中常见": "テキストの翻訳", "没给定指令": "テキストの翻訳", "还原原文": "テキストの翻訳", "自定义API KEY格式": "テキストの翻訳", "防止丢失最后一条消息": "テキストの翻訳", "方法": "テキストの翻訳", "压缩包": "テキストの翻訳", "对各个llm模型进行单元测试": "テキストの翻訳", "导入依赖失败": "テキストの翻訳", "详情信息见requirements.txt": "テキストの翻訳", "翻译内容可靠性无保障": "テキストの翻訳", "刷新页面即可以退出KnowledgeBaseQuestionAnswer模式": "テキストの翻訳", "上传本地文件/压缩包供函数插件调用": "テキストの翻訳", "循环监听已打开频道的消息": "テキストの翻訳", "一个包含所有切割音频片段文件路径的列表": "テキストの翻訳", "检测到arxiv文档连接": "テキストの翻訳", "P.S. 顺便把CTEX塞进去以支持中文": "テキストの翻訳", "后面是英文冒号": "テキストの翻訳", "上传文件自动修正路径": "テキストの翻訳", "实现消息发送、接收等功能": "メッセージの送受信などの機能を実現する", "改变输入参数的顺序与结构": "入力パラメータの順序と構造を変更する", "正在精细切分latex文件": "LaTeXファイルを細かく分割しています", "读取文件": "ファイルを読み込んでいます"}