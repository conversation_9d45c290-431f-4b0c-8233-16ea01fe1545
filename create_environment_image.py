import matplotlib.pyplot as plt
import numpy as np
import os

# 创建示例环境图片
def create_environment_image():
    """创建一个示例的海洋环境图片"""
    
    # 创建图像
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置海洋背景
    ax.set_xlim(0, 1200)
    ax.set_ylim(0, 800)
    ax.set_facecolor('#4682B4')  # 钢蓝色海洋
    
    # 添加一些岛屿/障碍物
    # 岛屿1
    island1_x = [200, 300, 350, 280, 180]
    island1_y = [150, 120, 200, 250, 220]
    ax.fill(island1_x, island1_y, color='#8B4513', alpha=0.8, label='岛屿')
    
    # 岛屿2
    island2_x = [800, 900, 950, 880, 750]
    island2_y = [500, 480, 550, 600, 580]
    ax.fill(island2_x, island2_y, color='#8B4513', alpha=0.8)
    
    # 添加一些浅滩区域
    shallow1_x = [400, 500, 550, 450]
    shallow1_y = [300, 280, 350, 370]
    ax.fill(shallow1_x, shallow1_y, color='#87CEEB', alpha=0.6, label='浅滩')
    
    # 添加航道标记
    ax.plot([50, 1150], [400, 400], 'r--', linewidth=2, alpha=0.7, label='推荐航道')
    
    # 添加起点和终点标记
    ax.plot(100, 400, 'go', markersize=15, label='起点')
    ax.plot(1100, 400, 'ro', markersize=15, label='终点')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 设置标题和标签
    ax.set_title('海洋环境示例图', fontsize=16, fontweight='bold')
    ax.set_xlabel('X坐标 (像素)', fontsize=12)
    ax.set_ylabel('Y坐标 (像素)', fontsize=12)
    
    # 添加图例
    ax.legend(loc='upper right')
    
    # 确保sample_data目录存在
    os.makedirs('sample_data', exist_ok=True)
    
    # 保存图片
    plt.savefig('sample_data/environment.png', dpi=150, bbox_inches='tight')
    print("环境图片已保存为: sample_data/environment.png")
    plt.close()

if __name__ == "__main__":
    create_environment_image()
