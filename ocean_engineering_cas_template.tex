%% 
%% Copyright 2019-2024 Elsevier Ltd
%% 
%% Template for Ocean Engineering Journal
%% Based on CAS Double Column Template
%% 

\documentclass[a4paper,fleqn]{cas-dc}

% If the frontmatter runs over more than one page
% use the longmktitle option.
%\documentclass[a4paper,fleqn,longmktitle]{cas-dc}

\usepackage[authoryear,longnamesfirst]{natbib}
\usepackage{amsmath,amsfonts,amssymb}
\usepackage{graphicx}

%%%Author macros
\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
%%%

\begin{document}
\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}

% Short title
\shorttitle{CEPPO Algorithm for Autonomous Ship Collision Avoidance}    

% Short author
\shortauthors{First Author et al.}  

% Main title of the paper
\title [mode = title]{New Cognitive Entropy Proximal Policy Optimization (CEPPO) Algorithm for Autonomous Ship Collision Avoidance based on Deep Reinforcement Learning}  

% Title footnote mark
\tnotemark[1] 

% Title footnote 1.
\tnotetext[1]{This research was supported by [Grant Information]} 

% First author
\author[1]{First Author}
\cormark[1]
\fnmark[1]
\ead{<EMAIL>}
\ead[url]{https://www.institution.edu/first-author}
\credit{Conceptualization, Methodology, Software, Writing - Original Draft}

% Address/affiliation
\affiliation[1]{organization={Department of Naval Architecture and Ocean Engineering},
            addressline={University Address}, 
            city={City},
            postcode={Postcode}, 
            state={State},
            country={Country}}

% Second author
\author[2]{Second Author}
\fnmark[2]
\ead{<EMAIL>}
\ead[url]{https://www.institution.edu/second-author}
\credit{Validation, Formal Analysis, Writing - Review \& Editing}

% Address/affiliation for second author
\affiliation[2]{organization={Department of Marine Engineering},
            addressline={University Address}, 
            city={City},
            postcode={Postcode}, 
            state={State},
            country={Country}}

% Corresponding author footnote
\cortext[cor1]{Corresponding author}
\fntext[fn1]{Present address: Department of Naval Architecture and Ocean Engineering}
\fntext[fn2]{Present address: Department of Marine Engineering}

\begin{abstract}
Due to the complexity of maritime environments and the high-dimensional nature of ship collision avoidance decisions, existing Deep Reinforcement Learning (DRL) algorithms exhibit significant deficiencies in balancing the exploration-exploitation trade-off in policy learning. Insufficient exploration in the early training stages causes the agent to prematurely converge to suboptimal solutions, thereby directly affecting the convergence rate and decision-making performance of the intelligent agent. In response to the aforementioned challenge, this research proposes a Cognitive Entropy-based Proximal Policy Optimization algorithm (CEPPO). This algorithm optimizes the training process through a three-stage dynamic adjustment mechanism: in the early training phase, it enhances exploration capability to effectively avoid local optima, while introducing entropy regularization and reward normalization mechanisms to reduce policy gradient variance; in the mid-training phase, it adopts adaptive balancing of exploration and exploitation to accelerate algorithm convergence; in the late training phase, it gradually decreases exploration to ensure policy stability and generalization capabilities. Additionally, this paper utilizes the Unreal Engine to construct a simulation platform for simulating complex dynamic scenarios. It also designs a multi-layered reward mechanism to further optimize collision avoidance strategies.
\end{abstract}

% Research highlights
\begin{highlights}
\item A cognitive entropy model is proposed for adaptive control of exploration behavior in unmanned boat collision avoidance strategies
\item A novel CEPPO algorithm achieves dynamic balance between exploration and exploitation in complex maritime environments
\item A high-fidelity simulation platform based on Unreal Engine 5 validates the algorithm's robustness and adaptability
\item The algorithm demonstrates superior performance in multi-ship collision avoidance scenarios compared to existing methods
\end{highlights}

% Keywords
\begin{keywords}
Autonomous ship collision avoidance \sep Cognitive entropy \sep Deep reinforcement learning \sep Proximal Policy Optimization (PPO) \sep Maritime safety \sep Unmanned surface vehicle
\end{keywords}

\maketitle

% Main text
\section{Introduction}\label{sec:introduction}

With the rapid development of the maritime economy and intelligent navigation, unmanned ship technology is increasingly becoming the focus of the shipping industry. Unmanned ships have advantages such as all-weather operation, remote control, and intelligent autonomous decision-making, which can effectively reduce labor costs and enhance maritime safety and efficiency. However, unmanned ships still face numerous technical challenges during actual operations, with collision avoidance being particularly prominent.

Currently, there have been numerous research achievements in the field of autonomous collision avoidance for unmanned ships. Traditional collision avoidance algorithms such as the A* algorithm, artificial potential field method, and genetic algorithms have been widely applied \citep{ref1,ref2}. However, such algorithms are relatively insufficient in autonomous decision-making and dynamic collision avoidance for unmanned boats.

For the risk assessment aspect of autonomous collision avoidance in unmanned ships, \citet{ref4} first proposed the ship domain model through statistical analysis of ship behavior in open waters. This model divides the area around ships into multiple virtual safety zones, triggering collision avoidance actions when obstacles enter these zones.

With the development of artificial intelligence, intelligent algorithms represented by reinforcement learning have received widespread attention in the field of ship collision avoidance \citep{ref8}. Deep reinforcement learning algorithms combine the perception capabilities of deep learning and the decision-making capabilities of reinforcement learning \citep{ref11}.

This paper introduces an innovative Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm, in which the change in entropy depends not only on training time but also on the model's cognition for nonlinear adjustments.

The main contributions of this study include:
\begin{enumerate}
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment.
\item A new CEPPO algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies.
\item A high-fidelity simulation platform based on Unreal Engine 5 is constructed for comprehensive validation.
\end{enumerate}

\section{Mathematical Model of Ship Motion and Collision Risk Assessment}\label{sec:mathematical_model}

This section presents the six degrees of freedom ship motion model of Unmanned Surface Vehicles (USVs) and the collision risk assessment method.

\subsection{Six Degrees of Freedom Ship Motion Dynamics Mathematical Model}\label{subsec:ship_motion_model}

The formulated USV kinematic model can accurately describe its motion characteristics in virtual environments using mathematical language. The system can be expressed as six coupled differential equations:

\begin{equation}
\begin{cases}
m(\dot{u} - rv + qw) = X_{thrust} + X_{drag} + X_{wave} + X_{current} \\
m(\dot{v} + ru - pw) = Y_{thrust} + Y_{drag} + Y_{wave} + Y_{current} \\
m(\dot{w} - qu + pv) = Z_{buoyancy} + Z_{drag} + Z_{wave} \\
I_x \dot{p} + (I_z - I_y)qr = K_{thrust} + K_{wave} \\
I_y \dot{q} + (I_x - I_z)pr = M_{wave} \\
I_z \dot{r} + (I_y - I_x)pq = N_{thrust} + N_{drag} + N_{wave} + N_{current}
\end{cases}
\label{eq:ship_motion}
\end{equation}

Where $I_x$, $I_y$, and $I_z$ represent the moments of inertia of the ship around the three principal axes, and $m$ is the ship's mass.

\subsection{Risk Assessment}\label{subsec:risk_assessment}

The collision risk assessment is based on DCPA (Distance to Closest Point of Approach) and TCPA (Time to Closest Point of Approach):

\begin{equation}
CR = \exp\left[-\frac{DCPA}{c_1}\right] - \exp\left[-\frac{TCPA}{c_2}\right]
\label{eq:collision_risk}
\end{equation}

\begin{equation}
DCPA = D \sin(\varphi_r - \alpha_{Tn} - \pi)
\label{eq:dcpa}
\end{equation}

\begin{equation}
TCPA = \frac{D \cos(\varphi_r - \alpha_{Tn} - \pi)}{v_r}
\label{eq:tcpa}
\end{equation}

\section{Cognitive Entropy-based Proximal Policy Optimization}\label{sec:ceppo}

This section introduces the CEPPO algorithm and its improvement principles.

\subsection{The Proximal Policy Optimization (PPO) Algorithm}\label{subsec:ppo_algorithm}

The process of ship collision avoidance can be defined as a Markov Decision Process (MDP). The optimal policy $\pi^*$ is obtained by:

\begin{equation}
\pi^* = \arg \max_{\pi_\theta} E_{\tau \sim \pi_\theta} \left[ \sum_{t=0}^{\infty} \gamma^t R(s_t, a_t) \right]
\label{eq:optimal_policy}
\end{equation}

PPO constrains the policy update step by introducing a clipped surrogate objective function:

\begin{equation}
L^{PPO}(\theta) = \hat{E}_t \left[ \min \left( r_t(\theta) \hat{A}_t, \text{clip}(r_t(\theta), 1-\varepsilon, 1+\varepsilon) \hat{A}_t \right) \right]
\label{eq:ppo_loss}
\end{equation}

\subsection{Reinforcement Learning Optimization Based on Dynamic Cognitive Entropy}\label{subsec:rl_optimization}

To enhance the capability of policy exploration, an entropy regularization term is introduced:

\begin{equation}
H(\pi(a_t | s_t)) = -\sum \pi(a_t | s_t) \log \pi(a_t | s_t)
\label{eq:entropy}
\end{equation}

The total loss function combines policy optimization, value function estimation, and exploration incentives:

\begin{equation}
L^{Total}(\theta, \omega) = \hat{E}_t \left[ L^{PPO}(\theta) - \lambda_1 L^{VF}(\omega) + \lambda_2 H(\pi(a_t | s_t)) \right]
\label{eq:total_loss}
\end{equation}

\section{Experimental Results and Analysis}\label{sec:experimental_results}

This section presents the experimental setup and results validation.

\section{Conclusion}\label{sec:conclusion}

This paper proposed a novel CEPPO algorithm for autonomous ship collision avoidance, demonstrating superior performance in complex maritime environments.

\section*{Declaration of competing interests}
The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

\section*{Data availability}
Data will be made available on request.

\section*{Acknowledgments}
This research was supported by [Grant Information].

% Bibliography
\bibliographystyle{cas-model2-names}

\begin{thebibliography}{99}

\bibitem{ref1}
Erckens, H., Büsser, G.-A., Pradalier, C., \& Siegwart, R.Y. (2010). Navigation strategy and trajectory following controller for an autonomous sailing vessel. IEEE RAM, 17, 47–54.

\bibitem{ref2}
Ye Li, Teng Ma, Pengyun Chen, Yanqing Jiang, Rupeng Wang \& Qiang Zhang. (2017). Autonomous underwater vehicle optimal path planning method for seabed terrain matching navigation. Ocean Engineering, 107-115.

\bibitem{ref3}
Sun, P. N., \& Zhang, Z. M. (2023). Smooth Path Planning for Unmanned Surface Vehicles Based on Ant Colony Algorithm. Electronic Science \& Technology, 36(3).

\bibitem{ref4}
Goodwin, Elisabeth M. (1975). A statistical study of ship domains. The Journal of Navigation, 28(3), 328-344.

\bibitem{ref5}
Mou, Jun Min, Cees Van der Tak, and Han Ligteringen. (2010). Study on collision avoidance in busy waterways by using AIS data. Ocean Engineering, 37(5-6), 483-490.

\bibitem{ref6}
Zhen, Rong, Maria Riveiro, and Yongxing Jin. (2017). A novel analytic framework of real-time multi-vessel collision risk assessment for maritime traffic surveillance. Ocean Engineering, 145, 492-501.

\bibitem{ref7}
谢朔, 初秀民, 柳晨光, 等. (2016). 船舶智能避碰研究综述及展望. 交通信息与安全, 34(01), 1-9.

\bibitem{ref8}
Sutton, Richard S., and Andrew G. Barto. (2018). Reinforcement learning: An introduction. MIT press.

\bibitem{ref9}
Yang, Y., Pang, Y. J., Li, H. W., \& Zhang, R. B. (2014). Research on USV Local Path Planning Method Based on Reinforcement Learning in Complex Sea Conditions. Journal of Marine Science and Application, 3(3), 333-339.

\bibitem{ref10}
Zhao, Dongbin, et al. (2016). Review of deep reinforcement learning and discussions on the development of computer Go. Control Theory \& Applications, 33(6), 701-717.

\bibitem{ref11}
Silver D, Huang A, Maddison C J, et al. (2016). Mastering the game of Go with deep neural networks and tree search. Nature, 529(7587), 484-489.

\bibitem{ref12}
Xu X, Lu Y, Liu X, et al. (2020). Intelligent collision avoidance algorithms for USVs via deep reinforcement learning under COLREGs. Ocean Engineering, 217, 107704.

\bibitem{ref13}
Zhao, J., Wang, P., Li, B., \& Bai, C. (2023). A ddpg-based usv path-planning algorithm. Applied Sciences, 13(19).

\bibitem{ref14}
Zhou, Z. G., Yu, S. Y., Yu, J. B., Duan, J. W., Chen, L., \& Chen, J. L. (2023). Research on T-DQN Intelligent Obstacle Avoidance Algorithm for Unmanned Surface Vehicles. Acta Automatica Sinica, 49(8), 1645-1655.

\bibitem{ref15}
Yuan, W., \& Rui, X. (2023). Deep reinforcement learning-based controller for dynamic positioning of an unmanned surface vehicle. Computers and Electrical Engineering.

\bibitem{ref16}
Zhao, J., Wang, P., Li, B., \& Bai, C. (2023). A ddpg-based usv path-planning algorithm. Applied Sciences, 13(19).

\bibitem{ref17}
Lai, P., Liu, Y., Zhang, W., \& Xu, H. (2023). Intelligent controller for unmanned surface vehicles by deep reinforcement learning. Physics of Fluids.

\bibitem{ref18}
Chen, G., Huang, Z., Wang, W., \& Yang, S. (2024). A Novel Dynamically Adjusted Entropy Algorithm for Collision Avoidance in Autonomous Ships Based on Deep Reinforcement Learning. Journal of Marine Science and Engineering.

\end{thebibliography}

\end{document}
