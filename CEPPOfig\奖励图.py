import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import matplotlib.ticker as ticker

# 读取本地CSV文件
file_path1 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx'
file_path2 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx'
file_path3 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx'
file_path4 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'

data1 = pd.read_excel(file_path1)
data2 = pd.read_excel(file_path2)
data3 = pd.read_excel(file_path3)
data4 = pd.read_excel(file_path4)

# 查看数据的前几行
print(data1.head())
print(data2.head())
print(data3.head())
print(data4.head())

# 过滤 'Step' 列数值在 20000 之前的数据
data1 = data1[data1['Step'] < 10000]
data2 = data2[data2['Step'] < 10000]
data3 = data3[data3['Step'] < 10000]
data4 = data4[data4['Step'] < 10000]

# 定义 Savitzky-Golay 滤波器平滑函数
def savitzky_golay_smoothing(data, window_size, poly_order):
    return savgol_filter(data, window_size, poly_order)

# 平滑数据
window_size = 29  # 选择合适的窗口大小，必须是奇数
poly_order = 3    # 多项式阶数

# 应用平滑并调整起点为0
smoothed1 = savitzky_golay_smoothing(data1['Value'], window_size, poly_order)
smoothed2 = savitzky_golay_smoothing(data2['Value'], window_size, poly_order)
smoothed3 = savitzky_golay_smoothing(data3['Value'], window_size, poly_order)
smoothed4 = savitzky_golay_smoothing(data4['Value'], window_size, poly_order)

# 调整起点为0
data1['smoothed_value'] = smoothed1 - smoothed1[0]
data2['smoothed_value'] = smoothed2 - smoothed2[0]
data3['smoothed_value'] = smoothed3 - smoothed3[0]
data4['smoothed_value'] = smoothed4 - smoothed4[0]

# 计算方差或置信区间
data1['std'] = data1['Value'].rolling(window=window_size, min_periods=1).std()
data2['std'] = data2['Value'].rolling(window=window_size, min_periods=1).std()
data3['std'] = data3['Value'].rolling(window=window_size, min_periods=1).std()
data4['std'] = data3['Value'].rolling(window=window_size, min_periods=1).std()

# 自定义数字格式
def custom_formatter(x, pos):
    return f'{x:,.0f}'

# 绘制平滑后的曲线和置信区间
plt.figure(figsize=(10, 6))

# 数据集1
plt.plot(data1['Step'], data1['smoothed_value'], label='CEPPO', color='#e76f51')
plt.fill_between(data1['Step'],
                 data1['smoothed_value'] - data1['std'],
                 data1['smoothed_value'] + data1['std'],
                 color='#e76f51', alpha=0.11)

# 数据集2
plt.plot(data2['Step'], data2['smoothed_value'], label='Improved PPO by Linear Entropy', color='#e8c56a')
plt.fill_between(data2['Step'],
                 data2['smoothed_value'] - data2['std'],
                 data2['smoothed_value'] + data2['std'],
                 color='#e8c56a', alpha=0.11)

# 数据集3
plt.plot(data3['Step'], data3['smoothed_value'], label='Improved PPO by Entropy', color='#299d91')
plt.fill_between(data3['Step'],
                 data3['smoothed_value'] - data3['std'],
                 data3['smoothed_value'] + data3['std'],
                 color='#299d91', alpha=0.11)

# 数据集4
plt.plot(data4['Step'], data4['smoothed_value'], label='PPO', color='#8bb17b')
plt.fill_between(data4['Step'],
                 data4['smoothed_value'] - data4['std'],
                 data4['smoothed_value'] + data4['std'],
                 color='#8bb17b', alpha=0.11)



# 添加标题和标签
plt.title('Average Reward over Time')
plt.xlabel('Episodes')
plt.ylabel('Reward')
plt.legend()

# 设置x轴和y轴的格式
plt.gca().xaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))
plt.gca().yaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))

# 添加网格
plt.grid(True, which='both', linestyle='--', linewidth=0.9)

# 显示图表
plt.show()