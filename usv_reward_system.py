"""
USV强化学习奖励系统
基于Unreal Engine蓝图奖励系统的Python实现

该实现复现了reward.txt中定义的USV学习训练器奖励机制，
包含路径跟踪、位置偏差惩罚等多重奖励组件。

作者：基于您的USV项目设计
日期：2025-07-04
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass


@dataclass
class USVState:
    """USV状态数据结构"""
    position: np.ndarray  # [x, y, z]
    velocity: np.ndarray  # [vx, vy, vz]
    heading: float        # 航向角 (弧度)
    agent_id: int        # 代理ID


@dataclass
class SplineData:
    """样条线数据结构"""
    points: List[np.ndarray]  # 样条线控制点
    
    def find_closest_point(self, world_location: np.ndarray) -> np.ndarray:
        """找到距离给定位置最近的样条线上的点"""
        if not self.points:
            return world_location
        
        min_distance = float('inf')
        closest_point = self.points[0]
        
        for point in self.points:
            distance = np.linalg.norm(world_location - point)
            if distance < min_distance:
                min_distance = distance
                closest_point = point
        
        return closest_point


class USVRewardSystem:
    """
    USV奖励系统类
    
    复现Unreal Engine蓝图中定义的奖励机制：
    1. 沿样条线速度奖励 (MakeRewardFromVelocityAlongSpline)
    2. 位置偏差惩罚 (MakeRewardOnLocationDifferenceAboveThreshold)
    3. 多重奖励组合
    """
    
    def __init__(self):
        """初始化奖励系统参数"""
        # 基于蓝图中的参数设置
        self.velocity_scale = 2000.0          # 速度缩放参数
        self.velocity_reward_scale = 10.0     # 速度奖励缩放
        self.distance_threshold = 5000.0      # 距离阈值
        self.distance_penalty_scale = -100.0  # 距离惩罚缩放
        self.finite_difference_delta = 10.0   # 有限差分增量
        
        # 调试和日志设置
        self.visual_logger_enabled = True
        self.debug_mode = False
        
    def make_reward_from_velocity_along_spline(self,
                                             spline: SplineData,
                                             location: np.ndarray,
                                             velocity: np.ndarray) -> float:
        """
        基于沿样条线速度计算奖励
        
        对应蓝图中的 MakeRewardFromVelocityAlongSpline 函数
        
        Args:
            spline: 样条线组件
            location: 物体位置
            velocity: 物体速度
            
        Returns:
            速度奖励值
        """
        # 找到样条线上最近的点
        closest_point = spline.find_closest_point(location)
        
        # 计算沿样条线的方向
        # 使用有限差分方法计算样条线在该点的切线方向
        delta_point = closest_point + np.array([self.finite_difference_delta, 0, 0])
        spline_direction = delta_point - closest_point
        
        if np.linalg.norm(spline_direction) > 0:
            spline_direction = spline_direction / np.linalg.norm(spline_direction)
        
        # 计算速度在样条线方向上的投影
        velocity_magnitude = np.linalg.norm(velocity)
        if velocity_magnitude > 0:
            velocity_normalized = velocity / velocity_magnitude
            velocity_along_spline = np.dot(velocity_normalized, spline_direction) * velocity_magnitude
        else:
            velocity_along_spline = 0.0
        
        # 计算奖励 (基于蓝图中的公式)
        normalized_velocity = velocity_along_spline / self.velocity_scale
        reward = normalized_velocity * self.velocity_reward_scale
        
        if self.debug_mode:
            print(f"速度沿样条线奖励: {reward:.4f}")
            print(f"  - 速度大小: {velocity_magnitude:.2f}")
            print(f"  - 沿样条线速度: {velocity_along_spline:.2f}")
        
        return reward
    
    def make_reward_on_location_difference_above_threshold(self,
                                                         location_a: np.ndarray,
                                                         location_b: np.ndarray) -> float:
        """
        基于位置差异超过阈值计算奖励/惩罚
        
        对应蓝图中的 MakeRewardOnLocationDifferenceAboveThreshold 函数
        
        Args:
            location_a: 位置A (实际位置)
            location_b: 位置B (目标位置)
            
        Returns:
            位置差异奖励/惩罚值
        """
        # 计算两点间距离
        distance = np.linalg.norm(location_a - location_b)
        
        # 如果距离超过阈值，给予惩罚
        if distance > self.distance_threshold:
            reward = self.distance_penalty_scale
        else:
            reward = 0.0
        
        if self.debug_mode:
            print(f"位置差异奖励: {reward:.4f}")
            print(f"  - 距离: {distance:.2f}")
            print(f"  - 阈值: {self.distance_threshold}")
        
        return reward
    
    def gather_agent_reward(self,
                          agent_id: int,
                          usv_state: USVState,
                          spline: SplineData,
                          target_location: Optional[np.ndarray] = None) -> float:
        """
        收集代理奖励 - 主要奖励计算函数
        
        对应蓝图中的 GatherAgentReward 函数
        
        Args:
            agent_id: 代理ID
            usv_state: USV状态
            spline: 路径样条线
            target_location: 目标位置 (可选)
            
        Returns:
            总奖励值
        """
        total_reward = 0.0
        
        # 1. 计算沿样条线速度奖励 (对应蓝图中的A输入)
        velocity_reward = self.make_reward_from_velocity_along_spline(
            spline, usv_state.position, usv_state.velocity)
        total_reward += velocity_reward
        
        # 2. 计算位置偏差惩罚 (对应蓝图中的D输入)
        if target_location is not None:
            closest_spline_point = spline.find_closest_point(usv_state.position)
            position_penalty = self.make_reward_on_location_difference_above_threshold(
                usv_state.position, closest_spline_point)
            total_reward += position_penalty
        
        # 3. 其他奖励组件 (对应蓝图中的B, C, E, F输入)
        # 这些可以根据具体需求添加
        
        if self.debug_mode:
            print(f"代理 {agent_id} 总奖励: {total_reward:.4f}")
            print(f"  - 速度奖励: {velocity_reward:.4f}")
            if target_location is not None:
                print(f"  - 位置惩罚: {position_penalty:.4f}")
        
        return total_reward
    
    def set_debug_mode(self, enabled: bool):
        """设置调试模式"""
        self.debug_mode = enabled
    
    def get_reward_config(self) -> Dict[str, Any]:
        """获取奖励系统配置信息"""
        return {
            'velocity_scale': self.velocity_scale,
            'velocity_reward_scale': self.velocity_reward_scale,
            'distance_threshold': self.distance_threshold,
            'distance_penalty_scale': self.distance_penalty_scale,
            'finite_difference_delta': self.finite_difference_delta,
            'visual_logger_enabled': self.visual_logger_enabled
        }


# 使用示例
if __name__ == "__main__":
    # 创建奖励系统
    reward_system = USVRewardSystem()
    reward_system.set_debug_mode(True)
    
    # 创建示例样条线
    spline_points = [
        np.array([0.0, 0.0, 0.0]),
        np.array([100.0, 0.0, 0.0]),
        np.array([200.0, 50.0, 0.0]),
        np.array([300.0, 100.0, 0.0])
    ]
    spline = SplineData(points=spline_points)
    
    # 创建USV状态
    usv_state = USVState(
        position=np.array([50.0, 10.0, 0.0]),
        velocity=np.array([15.0, 2.0, 0.0]),
        heading=0.1,
        agent_id=0
    )
    
    # 计算奖励
    reward = reward_system.gather_agent_reward(
        agent_id=0,
        usv_state=usv_state,
        spline=spline,
        target_location=np.array([300.0, 100.0, 0.0])
    )
    
    print(f"\n最终奖励值: {reward:.4f}")
    print("\n奖励系统配置:")
    config = reward_system.get_reward_config()
    for key, value in config.items():
        print(f"  {key}: {value}")


class AdvancedUSVRewardSystem(USVRewardSystem):
    """
    高级USV奖励系统

    扩展基础奖励系统，添加更多奖励组件：
    - 碰撞避免奖励
    - 能耗优化奖励
    - 任务完成奖励
    - 平滑控制奖励
    """

    def __init__(self):
        super().__init__()

        # 高级奖励参数
        self.collision_avoidance_weight = 50.0
        self.energy_efficiency_weight = 5.0
        self.task_completion_weight = 100.0
        self.smooth_control_weight = 10.0

        # 安全参数
        self.safety_radius = 150.0  # 安全半径
        self.min_safe_distance = 50.0  # 最小安全距离

        # 任务参数
        self.goal_tolerance = 25.0  # 目标容忍距离

    def calculate_collision_avoidance_reward(self,
                                           own_position: np.ndarray,
                                           obstacles: List[np.ndarray]) -> float:
        """计算碰撞避免奖励"""
        if not obstacles:
            return 0.0

        min_distance = float('inf')
        for obstacle in obstacles:
            distance = np.linalg.norm(own_position - obstacle)
            min_distance = min(min_distance, distance)

        if min_distance < self.min_safe_distance:
            # 危险区域，大惩罚
            reward = -self.collision_avoidance_weight * (self.min_safe_distance - min_distance) / self.min_safe_distance
        elif min_distance < self.safety_radius:
            # 警告区域，小惩罚
            reward = -self.collision_avoidance_weight * 0.1 * (self.safety_radius - min_distance) / self.safety_radius
        else:
            # 安全区域，小奖励
            reward = self.collision_avoidance_weight * 0.05

        return reward

    def calculate_energy_efficiency_reward(self,
                                         velocity: np.ndarray,
                                         control_input: np.ndarray) -> float:
        """计算能耗效率奖励"""
        # 基于速度和控制输入计算能耗
        speed = np.linalg.norm(velocity)
        control_magnitude = np.linalg.norm(control_input)

        # 鼓励适中的速度和平滑的控制
        optimal_speed = 10.0  # 最优速度
        speed_efficiency = 1.0 - abs(speed - optimal_speed) / optimal_speed
        control_efficiency = 1.0 / (1.0 + control_magnitude)

        reward = self.energy_efficiency_weight * (speed_efficiency + control_efficiency) / 2.0
        return reward

    def calculate_task_completion_reward(self,
                                       current_position: np.ndarray,
                                       goal_position: np.ndarray,
                                       previous_distance: float) -> Tuple[float, bool]:
        """计算任务完成奖励"""
        current_distance = np.linalg.norm(goal_position - current_position)

        # 检查是否到达目标
        task_completed = current_distance < self.goal_tolerance

        if task_completed:
            reward = self.task_completion_weight
        else:
            # 基于距离改善给予奖励
            progress = previous_distance - current_distance
            reward = progress * 2.0  # 进展奖励

        return reward, task_completed

    def calculate_smooth_control_reward(self,
                                      current_control: np.ndarray,
                                      previous_control: np.ndarray) -> float:
        """计算平滑控制奖励"""
        if previous_control is None:
            return 0.0

        control_change = np.linalg.norm(current_control - previous_control)
        # 惩罚剧烈的控制变化
        reward = -self.smooth_control_weight * control_change
        return reward

    def gather_advanced_agent_reward(self,
                                   agent_id: int,
                                   usv_state: USVState,
                                   spline: SplineData,
                                   goal_position: np.ndarray,
                                   obstacles: List[np.ndarray] = None,
                                   control_input: np.ndarray = None,
                                   previous_control: np.ndarray = None,
                                   previous_goal_distance: float = None) -> Dict[str, float]:
        """
        收集高级代理奖励

        Returns:
            包含各项奖励详情的字典
        """
        rewards = {}

        # 1. 基础奖励 (路径跟踪)
        base_reward = self.gather_agent_reward(agent_id, usv_state, spline)
        rewards['base_reward'] = base_reward

        # 2. 碰撞避免奖励
        if obstacles:
            collision_reward = self.calculate_collision_avoidance_reward(
                usv_state.position, obstacles)
            rewards['collision_avoidance'] = collision_reward
        else:
            rewards['collision_avoidance'] = 0.0

        # 3. 能耗效率奖励
        if control_input is not None:
            energy_reward = self.calculate_energy_efficiency_reward(
                usv_state.velocity, control_input)
            rewards['energy_efficiency'] = energy_reward
        else:
            rewards['energy_efficiency'] = 0.0

        # 4. 任务完成奖励
        if previous_goal_distance is not None:
            task_reward, completed = self.calculate_task_completion_reward(
                usv_state.position, goal_position, previous_goal_distance)
            rewards['task_completion'] = task_reward
            rewards['task_completed'] = completed
        else:
            rewards['task_completion'] = 0.0
            rewards['task_completed'] = False

        # 5. 平滑控制奖励
        if control_input is not None and previous_control is not None:
            smooth_reward = self.calculate_smooth_control_reward(
                control_input, previous_control)
            rewards['smooth_control'] = smooth_reward
        else:
            rewards['smooth_control'] = 0.0

        # 计算总奖励
        total_reward = sum([v for k, v in rewards.items()
                           if k not in ['task_completed']])
        rewards['total_reward'] = total_reward

        if self.debug_mode:
            print(f"\n代理 {agent_id} 高级奖励详情:")
            for key, value in rewards.items():
                if key != 'task_completed':
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")

        return rewards


def demo_advanced_reward_system():
    """演示高级奖励系统的使用"""
    print("=== USV高级奖励系统演示 ===\n")

    # 创建高级奖励系统
    advanced_reward_system = AdvancedUSVRewardSystem()
    advanced_reward_system.set_debug_mode(True)

    # 创建路径样条线 (模拟航道)
    spline_points = [
        np.array([0.0, 0.0, 0.0]),
        np.array([200.0, 0.0, 0.0]),
        np.array([400.0, 100.0, 0.0]),
        np.array([600.0, 200.0, 0.0]),
        np.array([800.0, 200.0, 0.0])
    ]
    spline = SplineData(points=spline_points)

    # 目标位置
    goal_position = np.array([800.0, 200.0, 0.0])

    # 障碍物位置
    obstacles = [
        np.array([300.0, 50.0, 0.0]),
        np.array([500.0, 150.0, 0.0])
    ]

    # 模拟USV运动序列
    scenarios = [
        {
            'name': '场景1: 正常航行',
            'usv_state': USVState(
                position=np.array([100.0, 5.0, 0.0]),
                velocity=np.array([12.0, 1.0, 0.0]),
                heading=0.08,
                agent_id=0
            ),
            'control_input': np.array([0.1, 0.05]),
            'previous_control': np.array([0.08, 0.04]),
            'previous_goal_distance': 720.0
        },
        {
            'name': '场景2: 接近障碍物',
            'usv_state': USVState(
                position=np.array([290.0, 45.0, 0.0]),
                velocity=np.array([10.0, 2.0, 0.0]),
                heading=0.15,
                agent_id=0
            ),
            'control_input': np.array([0.3, 0.2]),
            'previous_control': np.array([0.1, 0.05]),
            'previous_goal_distance': 520.0
        },
        {
            'name': '场景3: 接近目标',
            'usv_state': USVState(
                position=np.array([780.0, 195.0, 0.0]),
                velocity=np.array([8.0, 0.5, 0.0]),
                heading=0.02,
                agent_id=0
            ),
            'control_input': np.array([0.05, 0.02]),
            'previous_control': np.array([0.08, 0.03]),
            'previous_goal_distance': 30.0
        }
    ]

    # 运行各个场景
    for i, scenario in enumerate(scenarios):
        print(f"\n{'='*50}")
        print(f"{scenario['name']}")
        print(f"{'='*50}")

        # 计算当前距离目标的距离
        current_distance = np.linalg.norm(
            goal_position - scenario['usv_state'].position)
        print(f"当前距离目标: {current_distance:.2f}m")

        # 计算奖励
        rewards = advanced_reward_system.gather_advanced_agent_reward(
            agent_id=scenario['usv_state'].agent_id,
            usv_state=scenario['usv_state'],
            spline=spline,
            goal_position=goal_position,
            obstacles=obstacles,
            control_input=scenario['control_input'],
            previous_control=scenario['previous_control'],
            previous_goal_distance=scenario['previous_goal_distance']
        )

        print(f"\n总奖励: {rewards['total_reward']:.4f}")
        if rewards['task_completed']:
            print("🎉 任务完成!")

    print(f"\n{'='*50}")
    print("奖励系统配置信息:")
    print(f"{'='*50}")
    config = advanced_reward_system.get_reward_config()
    for key, value in config.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    # 运行基础示例
    print("=== 基础奖励系统示例 ===")
    # (之前的代码保持不变)

    # 运行高级示例
    print("\n" + "="*60)
    demo_advanced_reward_system()
