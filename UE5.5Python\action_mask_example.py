# -*- coding: utf-8 -*-
'''
船舶避碰动作遮蔽示例
这个文件展示了如何根据避碰规则生成动作掩码
'''

import numpy as np

# 定义船舶动作类型
class ShipActions:
    """船舶动作定义"""
    HARD_LEFT = 0      # 大幅左转
    SLIGHT_LEFT = 1    # 小幅左转  
    MAINTAIN = 2       # 保持航向
    SLIGHT_RIGHT = 3   # 小幅右转
    HARD_RIGHT = 4     # 大幅右转
    
    ACTION_NAMES = [
        "大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"
    ]
    
    @classmethod
    def get_action_name(cls, action_id):
        return cls.ACTION_NAMES[action_id]

# 定义会遇类型
class EncounterType:
    """会遇情况类型"""
    HEAD_ON = "对遇"           # 两船相向而行
    CROSSING = "交叉相遇"      # 两船航向交叉
    OVERTAKING = "追越"        # 一船追越另一船
    SAFE = "安全"             # 无碰撞风险

class CollisionAvoidanceRules:
    """避碰规则引擎 - 基于COLREG规则"""

    def __init__(self):
        self.action_count = 5  # 总共5个动作

    def normalize_bearing(self, bearing):
        """将方位角标准化到0-360度范围"""
        while bearing < 0:
            bearing += 360
        while bearing >= 360:
            bearing -= 360
        return bearing

    def analyze_encounter_situation(self, relative_bearing, has_collision_risk=True):
        """
        根据COLREG规则分析会遇情况

        参数:
        - relative_bearing: 目标船相对于本船的方位角 (度，0-360)
        - has_collision_risk: 是否存在碰撞风险

        返回:
        - encounter_type: 会遇类型
        - is_give_way: 是否为让路船
        - rule_number: 对应的COLREG规则编号
        """

        if not has_collision_risk:
            return EncounterType.SAFE, False, None

        # 标准化方位角到0-360度
        bearing = self.normalize_bearing(relative_bearing)

        # COLREGs 第14条：对遇情况
        if (bearing >= 350 and bearing <= 360) or (bearing >= 0 and bearing <= 10):
            return EncounterType.HEAD_ON, True, "Rule 14"  # 对遇时双方都需要右转

        # COLREGs 第15条：交叉相遇（让路船）
        elif bearing >= 5 and bearing <= 112.5:
            return EncounterType.CROSSING, True, "Rule 15"  # 目标船在右舷，本船为让路船

        # COLREGs 第17条：交叉相遇（直行船）
        elif bearing >= 247.5 and bearing <= 355:
            return EncounterType.CROSSING, False, "Rule 17"  # 目标船在左舷，本船为直行船

        # COLREGs 第13条：追越情况
        elif bearing >= 112.5 and bearing <= 247.5:
            return EncounterType.OVERTAKING, True, "Rule 13"  # 本船为追越船（让路船）

        else:
            return EncounterType.SAFE, False, None
    
    def generate_action_mask(self, encounter_type, is_give_way, rule_number, relative_bearing=0):
        """
        根据COLREG避碰规则生成精确的动作掩码

        参数:
        - encounter_type: 会遇类型
        - is_give_way: 是否为让路船
        - rule_number: COLREG规则编号
        - relative_bearing: 相对方位角

        返回:
        - action_mask: 布尔数组，True表示允许的动作
        """

        # 默认所有动作都允许
        mask = np.ones(self.action_count, dtype=bool)

        if encounter_type == EncounterType.HEAD_ON and rule_number == "Rule 14":
            # COLREGs 第14条：对遇情况
            # 两船均应右转避让，禁止左转和保持航向
            mask[ShipActions.HARD_LEFT] = False
            mask[ShipActions.SLIGHT_LEFT] = False
            mask[ShipActions.MAINTAIN] = False  # 对遇时不应保持航向
            print(f"Rule 14 - 对遇情况：必须右转避让，禁止左转和保持航向")

        elif encounter_type == EncounterType.CROSSING and rule_number == "Rule 15":
            # COLREGs 第15条：交叉相遇（让路船）
            # 目标船在右舷[005°,112.5°]，本船为让路船，应右转避让
            mask[ShipActions.HARD_LEFT] = False
            mask[ShipActions.SLIGHT_LEFT] = False
            mask[ShipActions.MAINTAIN] = False  # 让路船不能保持航向
            print(f"Rule 15 - 交叉相遇（让路船）：目标船在右舷，必须右转避让")

        elif encounter_type == EncounterType.CROSSING and rule_number == "Rule 17":
            # COLREGs 第17条：交叉相遇（直行船）
            # 目标船在左舷[247.5°,355°]，本船为直行船，应保持航向和速度
            # 但如果目标船未避让，仍需采取避碰行动
            print(f"Rule 17 - 交叉相遇（直行船）：目标船在左舷，保持航向（但准备避让）")
            # 在这种情况下，通常保持所有动作可用，但优先保持航向

        elif encounter_type == EncounterType.OVERTAKING and rule_number == "Rule 13":
            # COLREGs 第13条：追越情况
            # 本船为追越船（让路船），必须避让被追越船
            # 可从任一舷通过，但不能保持航向
            mask[ShipActions.MAINTAIN] = False  # 追越船不能保持航向
            print(f"Rule 13 - 追越情况：作为追越船必须避让，可从任一舷通过")

        elif encounter_type == EncounterType.SAFE:
            # 无碰撞风险，所有动作都允许
            print(f"安全情况：无碰撞风险，所有动作都允许")

        return mask
    
    def print_allowed_actions(self, action_mask):
        """打印允许的动作"""
        allowed_actions = []
        for i, allowed in enumerate(action_mask):
            if allowed:
                allowed_actions.append(ShipActions.get_action_name(i))
        
        print(f"允许的动作: {', '.join(allowed_actions)}")
        return allowed_actions

# 使用示例
def example_usage():
    """基于精确COLREG规则的使用示例"""
    rules = CollisionAvoidanceRules()

    print("=== 基于COLREG规则的船舶避碰动作遮蔽示例 ===\n")

    # 示例1：对遇情况 (Rule 14)
    print("示例1：对遇情况 (COLREGs Rule 14)")
    print("目标船相对方位: 005° (在[350°,010°]范围内)")
    encounter_type, is_give_way, rule_number = rules.analyze_encounter_situation(5, True)
    mask = rules.generate_action_mask(encounter_type, is_give_way, rule_number, 5)
    rules.print_allowed_actions(mask)
    print()

    # 示例2：交叉相遇 - 让路船 (Rule 15)
    print("示例2：交叉相遇 - 让路船 (COLREGs Rule 15)")
    print("目标船相对方位: 045° (在[005°,112.5°]范围内，目标船在右舷)")
    encounter_type, is_give_way, rule_number = rules.analyze_encounter_situation(45, True)
    mask = rules.generate_action_mask(encounter_type, is_give_way, rule_number, 45)
    rules.print_allowed_actions(mask)
    print()

    # 示例3：交叉相遇 - 直行船 (Rule 17)
    print("示例3：交叉相遇 - 直行船 (COLREGs Rule 17)")
    print("目标船相对方位: 300° (在[247.5°,355°]范围内，目标船在左舷)")
    encounter_type, is_give_way, rule_number = rules.analyze_encounter_situation(300, True)
    mask = rules.generate_action_mask(encounter_type, is_give_way, rule_number, 300)
    rules.print_allowed_actions(mask)
    print()

    # 示例4：追越情况 (Rule 13)
    print("示例4：追越情况 (COLREGs Rule 13)")
    print("目标船相对方位: 180° (在[112.5°,247.5°]范围内，目标船在后方)")
    encounter_type, is_give_way, rule_number = rules.analyze_encounter_situation(180, True)
    mask = rules.generate_action_mask(encounter_type, is_give_way, rule_number, 180)
    rules.print_allowed_actions(mask)
    print()

    # 示例5：边界情况测试
    print("示例5：边界情况测试")
    test_bearings = [0, 5, 10, 112.5, 247.5, 350, 355]
    for bearing in test_bearings:
        print(f"\n相对方位 {bearing}°:")
        encounter_type, is_give_way, rule_number = rules.analyze_encounter_situation(bearing, True)
        mask = rules.generate_action_mask(encounter_type, is_give_way, rule_number, bearing)
        allowed = rules.print_allowed_actions(mask)

if __name__ == "__main__":
    example_usage()
