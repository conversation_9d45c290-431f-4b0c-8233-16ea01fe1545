{"python.defaultInterpreter": "d:/code/.venv/Scripts/python.exe", "python.pythonPath": "d:/code/.venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "files.associations": {"*.py": "python"}, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.formatting.provider": "black", "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "args": ["-ExecutionPolicy", "Bypass"]}}}