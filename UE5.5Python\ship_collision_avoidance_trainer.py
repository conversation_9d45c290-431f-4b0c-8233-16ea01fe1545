# -*- coding: utf-8 -*-
'''
船舶避碰训练器
集成COLREG规则的动作遮蔽功能

这个文件展示了如何在实际的PPO训练中使用COLREG规则引擎
'''

import numpy as np
import torch
from colreg_rules import COLREGRules

class ShipCollisionAvoidanceTrainer:
    """
    船舶避碰训练器
    集成了COLREG规则的动作遮蔽功能
    """
    
    def __init__(self, action_count=5):
        """
        初始化训练器
        
        参数:
        - action_count: 动作空间大小
        """
        self.action_count = action_count
        self.colreg_rules = COLREGRules(action_count)
        
        # 统计信息
        self.rule_usage_stats = {
            "Head-on": 0,
            "Crossing (Give-way)": 0,
            "Crossing (Stand-on)": 0,
            "Overtaking": 0,
            "Safe": 0
        }
        
        self.masked_actions_count = 0
        self.total_actions_count = 0
    
    def process_observation_batch(self, observations):
        """
        处理观察批次，提取相对方位角和碰撞风险
        
        参数:
        - observations: 观察数据批次 [batch_size, obs_dim]
        
        返回:
        - relative_bearings: 相对方位角数组
        - collision_risks: 碰撞风险数组
        """
        # 这里需要根据您的观察空间结构来提取相关信息
        # 假设观察空间包含：[own_x, own_y, own_heading, target_x, target_y, target_heading, ...]
        
        batch_size = observations.shape[0]
        relative_bearings = np.zeros(batch_size)
        collision_risks = np.ones(batch_size, dtype=bool)  # 假设都有碰撞风险
        
        for i in range(batch_size):
            obs = observations[i]
            
            # 提取船舶位置和航向信息（需要根据实际观察空间调整）
            # 这里是示例代码，您需要根据实际的观察空间结构来修改
            if len(obs) >= 6:
                own_x, own_y, own_heading = obs[0], obs[1], obs[2]
                target_x, target_y, target_heading = obs[3], obs[4], obs[5]
                
                # 计算相对方位角
                dx = target_x - own_x
                dy = target_y - own_y
                
                if dx != 0 or dy != 0:
                    # 计算目标船相对于本船的方位角
                    angle_to_target = np.degrees(np.arctan2(dy, dx))
                    relative_bearing = (angle_to_target - own_heading) % 360
                    relative_bearings[i] = relative_bearing
                    
                    # 简单的碰撞风险判断（可以根据需要改进）
                    distance = np.sqrt(dx*dx + dy*dy)
                    collision_risks[i] = distance < 5.0  # 假设5个单位内有碰撞风险
        
        return relative_bearings, collision_risks
    
    def generate_action_masks_batch(self, observations):
        """
        为观察批次生成动作掩码
        
        参数:
        - observations: 观察数据批次 [batch_size, obs_dim]
        
        返回:
        - action_masks: 动作掩码批次 [batch_size, action_count]
        """
        relative_bearings, collision_risks = self.process_observation_batch(observations)
        batch_size = len(relative_bearings)
        
        action_masks = np.ones((batch_size, self.action_count), dtype=bool)
        
        for i in range(batch_size):
            # 生成单个样本的动作掩码
            mask = self.colreg_rules.generate_action_mask(
                relative_bearings[i], 
                collision_risks[i]
            )
            action_masks[i] = mask
            
            # 更新统计信息
            rule_name, _, _ = self.colreg_rules.get_rule_info(
                relative_bearings[i], 
                collision_risks[i]
            )
            self.rule_usage_stats[rule_name] += 1
            
            # 统计被遮蔽的动作数量
            self.masked_actions_count += np.sum(~mask)
            self.total_actions_count += self.action_count
        
        return action_masks
    
    def apply_action_masks_to_policy_output(self, policy_logits, action_masks):
        """
        将动作掩码应用到策略网络输出
        
        参数:
        - policy_logits: 策略网络输出的动作对数概率 [batch_size, action_count]
        - action_masks: 动作掩码 [batch_size, action_count]
        
        返回:
        - masked_logits: 应用掩码后的动作对数概率
        """
        if isinstance(policy_logits, torch.Tensor):
            # PyTorch张量处理
            masked_logits = policy_logits.clone()
            mask_tensor = torch.tensor(action_masks, device=policy_logits.device, dtype=torch.bool)
            masked_logits[~mask_tensor] = -1e10
            return masked_logits
        else:
            # NumPy数组处理
            masked_logits = policy_logits.copy()
            masked_logits[~action_masks] = -1e10
            return masked_logits
    
    def get_training_statistics(self):
        """
        获取训练统计信息
        
        返回:
        - dict: 包含规则使用统计和动作遮蔽统计的字典
        """
        total_encounters = sum(self.rule_usage_stats.values())
        
        stats = {
            "rule_usage": self.rule_usage_stats.copy(),
            "rule_usage_percentage": {},
            "masked_actions_count": self.masked_actions_count,
            "total_actions_count": self.total_actions_count,
            "masking_rate": self.masked_actions_count / max(self.total_actions_count, 1)
        }
        
        # 计算规则使用百分比
        for rule, count in self.rule_usage_stats.items():
            stats["rule_usage_percentage"][rule] = count / max(total_encounters, 1) * 100
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        for key in self.rule_usage_stats:
            self.rule_usage_stats[key] = 0
        self.masked_actions_count = 0
        self.total_actions_count = 0
    
    def print_statistics(self):
        """打印训练统计信息"""
        stats = self.get_training_statistics()
        
        print("=== 船舶避碰训练统计 ===")
        print(f"总动作数: {stats['total_actions_count']}")
        print(f"被遮蔽动作数: {stats['masked_actions_count']}")
        print(f"动作遮蔽率: {stats['masking_rate']:.2%}")
        print()
        
        print("规则使用统计:")
        for rule, percentage in stats["rule_usage_percentage"].items():
            count = stats["rule_usage"][rule]
            print(f"  {rule}: {count} 次 ({percentage:.1f}%)")

# 使用示例
def example_integration():
    """展示如何在训练中集成动作遮蔽"""
    
    # 创建训练器
    trainer = ShipCollisionAvoidanceTrainer(action_count=5)
    
    # 模拟一批观察数据
    batch_size = 32
    obs_dim = 10  # 假设观察空间维度为10
    
    # 生成模拟观察数据
    observations = np.random.randn(batch_size, obs_dim)
    
    # 为了演示，我们手动设置一些有意义的数据
    for i in range(batch_size):
        # 设置本船位置和航向
        observations[i, 0] = 0  # own_x
        observations[i, 1] = 0  # own_y  
        observations[i, 2] = 0  # own_heading
        
        # 设置目标船位置（不同方位）
        angle = i * 360 / batch_size  # 均匀分布在各个方位
        distance = 3.0  # 在碰撞风险范围内
        observations[i, 3] = distance * np.cos(np.radians(angle))  # target_x
        observations[i, 4] = distance * np.sin(np.radians(angle))  # target_y
        observations[i, 5] = (angle + 180) % 360  # target_heading (相向而行)
    
    # 生成动作掩码
    action_masks = trainer.generate_action_masks_batch(observations)
    
    # 模拟策略网络输出
    policy_logits = np.random.randn(batch_size, 5)
    
    # 应用动作掩码
    masked_logits = trainer.apply_action_masks_to_policy_output(policy_logits, action_masks)
    
    # 打印统计信息
    trainer.print_statistics()
    
    print(f"\n原始策略输出示例 (前5个样本):")
    print(policy_logits[:5])
    print(f"\n应用掩码后的输出:")
    print(masked_logits[:5])
    print(f"\n对应的动作掩码:")
    print(action_masks[:5])

if __name__ == "__main__":
    example_integration()
