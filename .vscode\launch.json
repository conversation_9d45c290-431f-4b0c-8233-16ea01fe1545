{"version": "0.2.0", "configurations": [{"name": "启动 GPT Academic", "type": "python", "request": "launch", "program": "${workspaceFolder}/gpt_academic-master/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/gpt_academic-master", "python": "${workspaceFolder}/.venv/Scripts/python.exe", "env": {"PYTHONPATH": "${workspaceFolder}/gpt_academic-master"}, "args": [], "justMyCode": false}, {"name": "启动 ChatPaper", "type": "python", "request": "launch", "program": "${workspaceFolder}/ChatPaper-main/HuggingFaceDeploy/app.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/ChatPaper-main", "python": "${workspaceFolder}/.venv/Scripts/python.exe", "env": {"PYTHONPATH": "${workspaceFolder}/ChatPaper-main"}, "args": [], "justMyCode": false}]}