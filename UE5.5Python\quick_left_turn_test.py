import torch

def apply_simple_left_turn_masking(act_logits):
    """禁止左转动作"""
    masked_logits = act_logits.clone()
    masked_logits[:, 0] = -1e10  # 大幅左转
    masked_logits[:, 1] = -1e10  # 小幅左转
    return masked_logits

# 测试
print("=== 简单左转遮蔽测试 ===")

action_names = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"]

# 原始输出
logits = torch.tensor([[1.0, 0.8, 0.2, 0.5, 0.3]])
probs = torch.softmax(logits, dim=-1)

print("\n原始概率:")
for i, (name, prob) in enumerate(zip(action_names, probs[0])):
    print(f"  {name}: {prob:.3f}")

# 应用遮蔽
masked_logits = apply_simple_left_turn_masking(logits)
masked_probs = torch.softmax(masked_logits, dim=-1)

print("\n遮蔽后概率:")
for i, (name, prob) in enumerate(zip(action_names, masked_probs[0])):
    status = "❌" if i in [0, 1] else "✅"
    print(f"  {status} {name}: {prob:.3f}")

print(f"\n✅ 左转遮蔽成功！左转概率变为0，其他动作概率重新分配")
print(f"💡 这就是您需要的简单实现：在算法中禁止左转，在奖励函数中实现详细规则")
