# 此Dockerfile适用于"无本地模型"的环境构建，如果需要使用chatglm等本地模型，请参考 docs/Dockerfile+ChatGLM
# - 1 修改 `config.py`
# - 2 构建 docker build -t gpt-academic-nolocal-latex -f docs/GithubAction+NoLocal+Latex .
# - 3 运行 docker run -v /home/<USER>/arxiv_cache:/root/arxiv_cache --rm -it --net=host gpt-academic-nolocal-latex

FROM menghuan1918/ubuntu_uv_ctex:latest
ENV DEBIAN_FRONTEND=noninteractive
SHELL ["/bin/bash", "-c"]
WORKDIR /gpt

# 先复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --break-system-packages openai numpy arxiv rich colorama Markdown pygments pymupdf python-docx pdfminer \
    && pip install --break-system-packages -r requirements.txt \
    && if [ "$(uname -m)" = "x86_64" ]; then \
    pip install --break-system-packages nougat-ocr; \
    fi \
    && pip cache purge \
    && rm -rf /root/.cache/pip/*

# 创建非root用户
RUN useradd -m gptuser && chown -R gptuser /gpt
USER gptuser

# 最后才复制代码文件,这样代码更新时只需重建最后几层，可以大幅减少docker pull所需的大小
COPY --chown=gptuser:gptuser . .

# 可选步骤，用于预热模块
RUN python3 -c 'from check_proxy import warm_up_modules; warm_up_modules()'

RUN python3 -m pip cache purge

# 启动
CMD ["python3", "-u", "main.py"]
