name: chatpaper
channels:
  - conda-forge
  - defaults
dependencies:
  - brotlipy=0.7.0
  - bzip2=1.0.8
  - ca-certificates=2021.10.8
  - cffi=1.15.0
  - cryptography=37.0.1
  - et_xmlfile=1.0.1
  - icu=69.1
  - idna=3.3
  - <PERSON><PERSON><PERSON>=2.14
  - libcxx=14.0.3
  - libffi=3.4.2
  - libiconv=1.16
  - libxml2=2.9.12
  - libzlib=1.2.11
  - ncurses=6.3
  - openpyxl=3.0.9
  - openssl=3.0.3
  - pcre=8.45
  - pip=22.1
  - pycparser=2.21
  - pymongo=4.0.1
  - pyopenssl=22.0.0
  - pysocks=1.7.1
  - python=3.8.13
  - python-dateutil=2.8.2
  - python_abi=3.8
  - readline=8.1
  - setuptools=62.2.0
  - six=1.16.0
  - sqlite=3.38.5
  - tk=8.6.12
  - uwsgi=2.0.20
  - wheel=0.37.1
  - xz=5.2.5
  - yaml=0.2.5
  - zlib=1.2.11
  - pip:
    - click==8.1.3
    - certifi==2021.10.8
    - charset-normalizer==2.0.12
    - crcmod==1.7
    - decorator==5.1.1
    - dicttoxml==1.7.4
    - et-xmlfile==1.1.0
    - fake-useragent==0.1.11
    - flask==2.0.3
    - flask-cors==3.0.10
    - itsdangerous==2.1.2
    - jinja2==3.1.2
    - markupsafe==2.1.1
    - openpyxl==3.0.9
    - py==1.11.0
    - pycryptodome==3.14.1
    - pyyaml==5.4.1
    - requests==2.27.1
    - retry==0.9.2
    - urllib3==1.26.8
    - werkzeug==2.1.2
prefix: /Users/<USER>/opt/miniconda3/envs/chatpaper
