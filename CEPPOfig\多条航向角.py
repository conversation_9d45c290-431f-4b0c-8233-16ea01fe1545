import pandas as pd
import matplotlib.pyplot as plt

# ===== 4 个 Excel 文件的路径 =====
file_paths = [
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/CEPPO.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/线性熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/固定熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/PPO.xlsx"
]

# ===== 图例标签 =====
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']

# ===== 颜色设置（可自定义）=====
colors = ["#e76f51", "#e8c56a", "#299d91", "#8bb17b"]

# ===== 创建图像 =====
plt.figure(figsize=(10, 6))

# ===== 遍历每个文件并画图 =====
for file_path, label, color in zip(file_paths, labels, colors):
    df = pd.read_excel(file_path)
    rudder_angles = df.iloc[:, 4]         # 第 5 列为舵角（索引从0开始）
    time = df.index * 0.3                  # 时间轴：帧号 × 0.3 秒
    plt.plot(time, rudder_angles, label=label, color=color)

# ===== 图像设置 =====
plt.xlabel('Time (s)')
plt.ylabel('Angle (°)')
plt.title('Heading Angle Variation Comparison')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
