06-25 23:43:45 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-25 23:43:45 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-25 23:43:47 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China - China Unicom Fujian Province Network
06-25 23:43:47 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-25 23:43:47 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-25 23:43:47 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:91 - [1;31m[API_KEY] 您的 API_KEY（sk-e730e02f428e***）不满足任何一种已知的密钥格式，请在config文件中修改API密钥之后再运行（详见`https://github.com/binary-husky/gpt_academic/wiki/api_key`）。[0m
06-25 23:43:47 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-25 23:43:47 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:49201
06-25 23:43:48 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-25 23:43:53 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-25 23:43:53 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-25 23:43:53 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-25 23:43:53 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-25 23:43:53 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 00:45:58 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-26 00:45:58 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-26 00:46:03 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China
06-26 00:46:03 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-26 00:46:03 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-26 00:46:03 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:91 - [1;31m[API_KEY] 您的 API_KEY（***）不满足任何一种已知的密钥格式，请在config文件中修改API密钥之后再运行（详见`https://github.com/binary-husky/gpt_academic/wiki/api_key`）。[0m
06-26 00:46:03 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-26 00:46:03 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:51203
06-26 00:46:04 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-26 00:46:09 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-26 00:46:09 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 00:46:09 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 00:46:09 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 00:46:09 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 11:55:40 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-26 11:55:40 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-26 11:55:43 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China - China Unicom Fujian Province Network
06-26 11:55:43 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-26 11:55:43 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-26 11:55:43 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:91 - [1;31m[API_KEY] 您的 API_KEY（***）不满足任何一种已知的密钥格式，请在config文件中修改API密钥之后再运行（详见`https://github.com/binary-husky/gpt_academic/wiki/api_key`）。[0m
06-26 11:55:43 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-26 11:55:43 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:51526
06-26 11:55:49 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-26 11:55:49 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 11:55:49 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 11:55:49 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 11:55:49 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 11:55:49 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-26 22:24:35 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-26 22:24:35 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-26 22:24:38 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China - China Unicom Fujian Province Network
06-26 22:24:38 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-26 22:24:38 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-26 22:24:38 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-26 22:24:38 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-26 22:24:38 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:52103
06-26 22:24:39 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-26 22:24:44 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-26 22:24:44 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 22:24:44 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 22:24:44 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 22:24:44 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 22:26:53 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-26 22:26:53 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-26 22:26:58 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China - China Unicom Fujian Province Network
06-26 22:26:58 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-26 22:26:58 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-26 22:26:58 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-26 22:26:58 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-26 22:26:58 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:52292
06-26 22:27:00 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-26 22:27:04 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-26 22:27:04 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 22:27:04 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 22:27:04 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 22:27:04 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 22:28:54 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-26 22:28:54 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-26 22:29:00 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China - China Unicom Fujian Province Network
06-26 22:29:00 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-26 22:29:00 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-26 22:29:00 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-26 22:29:00 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-26 22:29:00 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:55225
06-26 22:29:05 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-26 22:29:06 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-26 22:29:06 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 22:29:06 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-26 22:29:06 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-26 22:29:06 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-28 13:48:42 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-28 13:48:43 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-28 13:48:44 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
06-28 13:48:44 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-28 13:48:44 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-28 13:48:44 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-28 13:48:44 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-28 13:48:44 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:52173
06-28 13:48:46 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-28 13:48:50 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-28 13:48:50 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-28 13:48:50 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-28 13:48:50 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-28 13:48:50 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-28 13:50:15 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-28 13:50:15 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-28 13:50:16 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
06-28 13:50:16 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-28 13:50:16 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-28 13:50:16 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-28 13:50:17 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-28 13:50:17 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:52320
06-28 13:50:17 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-28 13:50:23 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-28 13:50:23 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-28 13:50:23 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-28 13:50:23 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-28 13:50:23 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-28 14:13:26 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-28 14:13:26 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-28 14:13:27 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China
06-28 14:13:27 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-28 14:13:27 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-28 14:13:27 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-28 14:13:27 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-28 14:13:27 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:49874
06-28 14:13:28 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-28 14:13:33 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-28 14:13:33 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-28 14:13:33 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-28 14:13:33 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-28 14:13:33 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-30 16:12:33 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
06-30 16:12:33 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
06-30 16:12:35 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
06-30 16:12:35 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
06-30 16:12:35 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
06-30 16:12:35 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
06-30 16:12:35 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
06-30 16:12:35 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:63682
06-30 16:12:36 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
06-30 16:12:41 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
06-30 16:12:41 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-30 16:12:41 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
06-30 16:12:41 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
06-30 16:12:41 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 11:10:36 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 11:10:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 11:10:37 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China
07-01 11:10:37 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 11:10:37 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 11:10:37 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 11:10:37 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 11:10:37 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:64823
07-01 11:10:43 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 11:10:43 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 11:10:43 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 11:10:43 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 11:10:43 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 11:10:44 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 11:56:35 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 11:56:35 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 11:56:36 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 11:56:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 11:56:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 11:56:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 11:56:36 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 11:56:36 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:51129
07-01 11:56:37 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 11:56:42 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 11:56:42 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 11:56:42 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 11:56:42 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 11:56:42 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:02:35 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 12:02:35 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 12:02:36 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 12:02:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 12:02:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 12:02:36 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 12:02:36 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 12:02:36 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:51569
07-01 12:02:37 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 12:02:42 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 12:02:42 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:02:42 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:02:42 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:02:42 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:12:15 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 12:12:15 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 12:12:16 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 12:12:16 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 12:12:16 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 12:12:16 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 12:12:16 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 12:12:16 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:52173
07-01 12:12:17 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 12:12:22 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 12:12:22 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:12:23 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:12:23 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:12:23 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:18:27 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 12:18:27 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 12:18:28 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 12:18:28 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 12:18:28 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 12:18:28 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 12:18:28 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 12:18:28 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:52690
07-01 12:18:29 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 12:18:34 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 12:18:34 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:18:34 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:18:34 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:18:34 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:22:15 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 12:22:15 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 12:22:17 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 12:22:17 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 12:22:17 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 12:22:17 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 12:22:17 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 12:22:17 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:53034
07-01 12:22:18 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 12:22:23 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 12:22:23 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:22:23 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:22:23 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:22:23 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:26:04 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 12:26:04 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 12:26:06 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 12:26:06 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 12:26:06 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 12:26:06 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 12:26:06 | INFO     | __main__:run_delayed_tasks:343 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 12:26:06 | INFO     | __main__:run_delayed_tasks:344 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:53396
07-01 12:26:07 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 12:26:12 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 12:26:12 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:26:12 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 12:26:12 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 12:26:12 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 15:51:47 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-01 15:51:47 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-01 15:51:49 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-01 15:51:49 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-01 15:51:49 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-01 15:51:49 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-01 15:51:49 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-01 15:51:49 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:50195
07-01 15:51:50 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-01 15:51:55 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-01 15:51:55 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 15:51:55 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-01 15:51:55 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-01 15:51:55 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-02 17:39:23 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-02 17:39:23 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-02 17:39:25 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-02 17:39:25 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-02 17:39:25 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-02 17:39:25 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-02 17:39:25 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-02 17:39:25 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:64557
07-02 17:39:26 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-02 17:39:31 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-02 17:39:31 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-02 17:39:31 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-02 17:39:31 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-02 17:39:31 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-07 13:37:48 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-07 13:37:48 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-07 13:37:50 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-07 13:37:50 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-07 13:37:50 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-07 13:37:50 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-07 13:37:50 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-07 13:37:50 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:49887
07-07 13:37:51 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-07 13:37:56 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-07 13:37:56 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-07 13:37:56 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-07 13:37:56 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-07 13:37:56 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-08 17:52:56 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-08 17:52:56 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-08 17:52:57 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-08 17:52:57 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-08 17:52:57 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-08 17:52:57 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-08 17:52:57 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-08 17:52:57 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:60695
07-08 17:53:01 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-08 17:53:03 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-08 17:53:03 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-08 17:53:04 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-08 17:53:04 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-08 17:53:04 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-11 14:29:02 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-11 14:29:02 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-11 14:29:04 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：China
07-11 14:29:04 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-11 14:29:04 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-11 14:29:04 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-11 14:29:04 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-11 14:29:04 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:56613
07-11 14:29:10 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-11 14:29:10 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-11 14:29:10 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-11 14:29:10 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-11 14:29:10 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-11 14:29:10 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-12 12:45:50 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-12 12:45:50 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-12 12:45:51 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：United States
07-12 12:45:51 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-12 12:45:51 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-12 12:45:51 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-12 12:45:51 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-12 12:45:51 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:54159
07-12 12:45:52 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-12 12:45:57 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-12 12:45:57 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-12 12:45:58 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-12 12:45:58 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-12 12:45:58 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-13 16:12:05 | WARNING  | shared_utils.logging:setup_logging:60 - 所有对话记录将自动保存在本地目录gpt_log\admin\chat_secrets.log, 请注意自我隐私保护哦！
07-13 16:12:05 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:95 - [1;31m[PROXY] 网络代理状态：未配置。无代理状态下很可能无法访问OpenAI家族的模型。建议：检查USE_PROXY选项是否修改。[0m
07-13 16:12:06 | WARNING  | check_proxy:check_proxy:35 - 代理配置 无, 代理所在地：Hong Kong
07-13 16:12:06 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:86 - [1;34m[API_KEY] 本项目现已支持OpenAI和Azure的api-key。也支持同时填写多个api-key，如API_KEY="openai-key1,openai-key2,azure-key3"[0m
07-13 16:12:06 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:87 - [1;34m[API_KEY] 您既可以在config.py中修改api-key(s)，也可以在问题输入区输入临时的api-key(s)，然后回车键提交后即可生效。[0m
07-13 16:12:06 | INFO     | shared_utils.config_loader:read_single_conf_with_lru_cache:89 - [1;32m[API_KEY] 您的 API_KEY 是: sk-proj-AMhlRhD*** API_KEY 导入成功[0m
07-13 16:12:06 | INFO     | __main__:run_delayed_tasks:337 - 如果浏览器没有自动打开，请复制并转到以下URL：
07-13 16:12:06 | INFO     | __main__:run_delayed_tasks:338 - 	「暗色主题已启用（支持动态切换主题）」: http://localhost:60805
07-13 16:12:06 | INFO     | check_proxy:auto_update:219 - 自动更新程序：已禁用。建议排查：代理网络配置。
07-13 16:12:12 | INFO     | check_proxy:warm_up_modules:225 - 正在执行一些模块的预热 ...
07-13 16:12:12 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-13 16:12:12 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
07-13 16:12:12 | INFO     | request_llms.bridge_all:get_encoder:58 - 正在加载tokenizer，如果是第一次运行，可能需要一点时间下载参数
07-13 16:12:12 | INFO     | request_llms.bridge_all:get_encoder:60 - 加载tokenizer完毕
