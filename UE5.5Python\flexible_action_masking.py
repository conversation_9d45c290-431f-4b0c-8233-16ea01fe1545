# -*- coding: utf-8 -*-
'''
灵活动作遮蔽策略
展示不同严格程度的动作遮蔽方法，适用于复杂的船舶导航场景
'''

import numpy as np
from colreg_rules import COLREGRules

class FlexibleActionMasking:
    """
    灵活的动作遮蔽策略
    根据不同情况调整遮蔽的严格程度
    """
    
    def __init__(self):
        self.rules = COLREGRules()
        self.action_names = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"]
    
    def determine_strictness_level(self, situation):
        """
        根据情况确定严格程度
        
        参数:
        - situation: 包含环境信息的字典
        
        返回:
        - str: 严格程度级别
        """
        distance = situation.get('distance', 5.0)
        time_to_collision = situation.get('time_to_collision', 10.0)
        multiple_targets = situation.get('multiple_targets', False)
        near_destination = situation.get('near_destination', False)
        
        # 紧急情况：距离很近或即将碰撞
        if distance < 1.0 or time_to_collision < 3.0:
            return "emergency"
        
        # 灵活模式：多船情况或接近目的地
        elif multiple_targets or near_destination:
            return "flexible"
        
        # 严格模式：训练初期或明确的单船场景
        elif situation.get('training_phase') == "early":
            return "strict"
        
        # 正常模式：大多数情况
        else:
            return "normal"
    
    def get_action_strategy(self, relative_bearing, situation):
        """
        获取完整的动作策略
        
        返回:
        - dict: 包含掩码、建议动作、严格程度等信息
        """
        strictness = self.determine_strictness_level(situation)
        has_risk = situation.get('has_collision_risk', True)
        
        # 生成动作掩码
        mask = self.rules.generate_action_mask(relative_bearing, has_risk, strictness)
        
        # 获取规则信息
        rule_name, rule_desc, rule_num = self.rules.get_rule_info(relative_bearing, has_risk)
        
        # 生成建议动作
        preferred_actions = self.get_preferred_actions(relative_bearing, rule_name, strictness)
        
        return {
            'mask': mask,
            'strictness': strictness,
            'rule_name': rule_name,
            'rule_description': rule_desc,
            'rule_number': rule_num,
            'preferred_actions': preferred_actions,
            'allowed_actions': [self.action_names[i] for i, allowed in enumerate(mask) if allowed],
            'forbidden_actions': [self.action_names[i] for i, allowed in enumerate(mask) if not allowed]
        }
    
    def get_preferred_actions(self, relative_bearing, rule_name, strictness):
        """获取推荐的动作优先级"""
        if rule_name == "Head-on":
            if strictness == "emergency":
                return ["大幅右转", "小幅右转", "大幅左转"]  # 紧急情况任何避让都可以
            else:
                return ["小幅右转", "大幅右转"]  # 正常情况右转优先
                
        elif rule_name == "Crossing (Give-way)":
            if strictness == "emergency":
                return ["大幅右转", "小幅右转", "大幅左转"]
            else:
                return ["小幅右转", "大幅右转"]
                
        elif rule_name == "Crossing (Stand-on)":
            return ["保持航向", "小幅右转", "小幅左转"]  # 直行船优先保持航向
            
        elif rule_name == "Overtaking":
            return ["小幅左转", "小幅右转", "大幅左转", "大幅右转"]  # 可从任一舷通过
            
        else:
            return ["保持航向", "小幅右转", "小幅左转"]

class AdaptiveRewardSystem:
    """
    自适应奖励系统
    根据动作是否符合规则给予不同程度的奖励/惩罚
    """
    
    def __init__(self):
        self.masking_system = FlexibleActionMasking()
    
    def calculate_action_reward(self, action, relative_bearing, situation):
        """
        计算动作奖励
        
        参数:
        - action: 执行的动作索引
        - relative_bearing: 相对方位角
        - situation: 环境情况
        
        返回:
        - float: 奖励值
        """
        strategy = self.masking_system.get_action_strategy(relative_bearing, situation)
        action_name = self.masking_system.action_names[action]
        
        base_reward = 0.1  # 基础奖励
        
        # 检查动作是否被禁止
        if not strategy['mask'][action]:
            # 根据严格程度给予不同惩罚
            if strategy['strictness'] == "strict":
                return -2.0  # 严格模式重大惩罚
            elif strategy['strictness'] == "normal":
                return -1.0  # 正常模式中等惩罚
            elif strategy['strictness'] == "flexible":
                return -0.3  # 灵活模式轻微惩罚
            else:  # emergency
                return base_reward  # 紧急情况不惩罚
        
        # 检查是否是推荐动作
        if action_name in strategy['preferred_actions']:
            priority = strategy['preferred_actions'].index(action_name)
            bonus = 0.5 - priority * 0.1  # 优先级越高奖励越多
            return base_reward + bonus
        
        return base_reward

def demonstrate_flexible_masking():
    """演示灵活动作遮蔽的效果"""
    
    print("=== 灵活动作遮蔽策略演示 ===\n")
    
    masking_system = FlexibleActionMasking()
    reward_system = AdaptiveRewardSystem()
    
    # 测试场景
    scenarios = [
        {
            'name': '正常对遇',
            'bearing': 0,
            'situation': {
                'distance': 3.0,
                'time_to_collision': 8.0,
                'multiple_targets': False,
                'near_destination': False,
                'training_phase': 'normal'
            }
        },
        {
            'name': '紧急对遇',
            'bearing': 0,
            'situation': {
                'distance': 0.8,
                'time_to_collision': 2.0,
                'multiple_targets': False,
                'near_destination': False,
                'training_phase': 'normal'
            }
        },
        {
            'name': '多船复杂场景',
            'bearing': 45,
            'situation': {
                'distance': 2.5,
                'time_to_collision': 6.0,
                'multiple_targets': True,
                'near_destination': False,
                'training_phase': 'normal'
            }
        },
        {
            'name': '接近目的地',
            'bearing': 300,
            'situation': {
                'distance': 4.0,
                'time_to_collision': 10.0,
                'multiple_targets': False,
                'near_destination': True,
                'training_phase': 'normal'
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"场景: {scenario['name']}")
        print("-" * 40)
        
        strategy = masking_system.get_action_strategy(
            scenario['bearing'], 
            scenario['situation']
        )
        
        print(f"相对方位角: {scenario['bearing']}°")
        print(f"适用规则: Rule {strategy['rule_number']} - {strategy['rule_name']}")
        print(f"严格程度: {strategy['strictness']}")
        print(f"允许动作: {', '.join(strategy['allowed_actions'])}")
        print(f"推荐动作: {', '.join(strategy['preferred_actions'][:3])}")
        
        if strategy['forbidden_actions']:
            print(f"禁止动作: {', '.join(strategy['forbidden_actions'])}")
        
        # 计算各动作的奖励
        print("\n动作奖励:")
        for i, action_name in enumerate(masking_system.action_names):
            reward = reward_system.calculate_action_reward(
                i, scenario['bearing'], scenario['situation']
            )
            status = "✅" if strategy['mask'][i] else "❌"
            print(f"  {status} {action_name}: {reward:+.1f}")
        
        print("\n" + "="*50 + "\n")

def compare_strategies():
    """对比不同策略的效果"""
    
    print("=== 策略对比分析 ===\n")
    
    masking_system = FlexibleActionMasking()
    
    # 对遇场景下的不同严格程度
    bearing = 0  # 对遇
    
    strictness_levels = ["strict", "normal", "flexible", "emergency"]
    
    print("对遇场景下不同严格程度的动作限制:")
    print("-" * 50)
    
    for strictness in strictness_levels:
        mask = masking_system.rules.generate_action_mask(bearing, True, strictness)
        allowed = [masking_system.action_names[i] for i, allowed in enumerate(mask) if allowed]
        forbidden = [masking_system.action_names[i] for i, allowed in enumerate(mask) if not allowed]
        
        print(f"\n{strictness.upper()} 模式:")
        print(f"  允许: {', '.join(allowed)}")
        print(f"  禁止: {', '.join(forbidden) if forbidden else '无'}")
        print(f"  遮蔽率: {(len(forbidden)/len(masking_system.action_names)*100):.0f}%")

if __name__ == "__main__":
    # 演示灵活遮蔽
    demonstrate_flexible_masking()
    
    # 对比不同策略
    compare_strategies()
    
    print("\n🎯 总结:")
    print("- 严格模式: 训练初期，确保学习正确规则")
    print("- 正常模式: 大多数情况，平衡规则和灵活性") 
    print("- 灵活模式: 复杂场景，允许更多选择")
    print("- 紧急模式: 危险情况，安全优先于规则")
