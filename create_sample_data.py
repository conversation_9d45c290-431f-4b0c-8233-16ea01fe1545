import pandas as pd
import numpy as np
import os

# 创建示例数据目录
os.makedirs('sample_data', exist_ok=True)

# 生成示例轨迹数据
def create_trajectory_data(filename, num_points=100):
    """创建示例轨迹数据"""
    # 生成模拟的船舶轨迹数据
    t = np.linspace(0, 10, num_points)
    
    # x, y坐标 (世界坐标)
    x = 1000 + 50 * t + 10 * np.sin(0.5 * t)
    y = 500 + 30 * t + 5 * np.cos(0.3 * t)
    
    # x, y速度
    vx = 50 + 5 * np.cos(0.5 * t)
    vy = 30 - 1.5 * np.sin(0.3 * t)
    
    # 舵角 (度)
    rudder_angle = 5 * np.sin(0.2 * t)
    
    # 航向角 (度)
    heading = np.arctan2(vy, vx) * 180 / np.pi + np.random.normal(0, 2, num_points)
    
    data = pd.DataFrame({
        'x': x,
        'y': y,
        'vx': vx,
        'vy': vy,
        'rudder_angle': rudder_angle,
        'heading': heading
    })
    
    data.to_excel(f'sample_data/{filename}', index=False, header=False)
    print(f"Created {filename}")

# 生成奖励数据
def create_reward_data(filename, algorithm_name):
    """创建示例奖励数据"""
    steps = np.arange(0, 10000, 10)
    
    # 根据算法类型生成不同的奖励曲线
    if 'CEPPO' in algorithm_name:
        base_reward = -200 + 300 * (1 - np.exp(-steps/2000))
        noise = np.random.normal(0, 20, len(steps))
    elif 'Entropy' in algorithm_name:
        base_reward = -250 + 280 * (1 - np.exp(-steps/2500))
        noise = np.random.normal(0, 25, len(steps))
    elif 'PPO' in algorithm_name:
        base_reward = -300 + 250 * (1 - np.exp(-steps/3000))
        noise = np.random.normal(0, 30, len(steps))
    else:
        base_reward = -280 + 260 * (1 - np.exp(-steps/2800))
        noise = np.random.normal(0, 28, len(steps))
    
    rewards = base_reward + noise
    
    data = pd.DataFrame({
        'Step': steps,
        'Value': rewards
    })
    
    data.to_excel(f'sample_data/{filename}', index=False)
    print(f"Created {filename}")

# 创建轨迹数据文件
create_trajectory_data('CEPPO.xlsx')
create_trajectory_data('线性熵.xlsx')
create_trajectory_data('固定熵.xlsx')
create_trajectory_data('PPO.xlsx')
create_trajectory_data('预设轨迹.xlsx')
create_trajectory_data('1.xlsx')  # 用于航向角变化

# 创建奖励数据文件
create_reward_data('CEPPO_reward.xlsx', 'CEPPO')
create_reward_data('Improved_PPO_by_Linear_Entropy.xlsx', 'Linear Entropy')
create_reward_data('Improved_PPO_by_Entropy.xlsx', 'Entropy')
create_reward_data('Classic_PPO.xlsx', 'PPO')

print("所有示例数据文件创建完成！")
