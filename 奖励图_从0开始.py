import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import matplotlib.ticker as ticker
import numpy as np

# 读取本地Excel文件
file_path1 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx'
file_path2 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx'
file_path3 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx'
file_path4 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'

data1 = pd.read_excel(file_path1)
data2 = pd.read_excel(file_path2)
data3 = pd.read_excel(file_path3)
data4 = pd.read_excel(file_path4)

# 过滤 'Step' 列数值在 10000 之前的数据
data1 = data1[data1['Step'] < 10000]
data2 = data2[data2['Step'] < 10000]
data3 = data3[data3['Step'] < 10000]
data4 = data4[data4['Step'] < 10000]

datasets = [data1, data2, data3, data4]
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']

print("=== 确保图从0开始的解决方案 ===")

# 方法1: 在数据开头添加更多的0值点来稳定平滑
def add_zero_padding(data, num_padding=15):
    """在数据开头添加0值点来稳定平滑效果"""
    # 创建填充数据
    first_step = data['Step'].iloc[0]
    step_interval = 32  # 根据您的数据，Step间隔是32
    
    padding_steps = []
    for i in range(num_padding, 0, -1):
        padding_steps.append(first_step - i * step_interval)
    
    # 创建填充DataFrame
    padding_data = pd.DataFrame({
        'Wall time': [0] * num_padding,
        'Step': padding_steps,
        'Value': [0.0] * num_padding
    })
    
    # 合并数据
    padded_data = pd.concat([padding_data, data], ignore_index=True)
    return padded_data

# 方法2: 平滑后手动调整起点
def adjust_smoothed_start_to_zero(data, smoothed_values):
    """平滑后将起点调整为0"""
    # 计算起点偏移
    start_offset = smoothed_values[0]
    
    # 调整所有平滑值
    adjusted_values = smoothed_values - start_offset
    
    return adjusted_values, start_offset

# 选择解决方案
SOLUTION = 2  # 1=添加填充数据, 2=平滑后调整起点

if SOLUTION == 1:
    print("使用方案1: 添加填充数据")
    # 为每个数据集添加填充
    padded_datasets = []
    for data, label in zip(datasets, labels):
        padded_data = add_zero_padding(data, num_padding=15)
        padded_datasets.append(padded_data)
        print(f"{label}: 添加了15个填充点，新数据长度: {len(padded_data)}")
    
    datasets = padded_datasets

print(f"\n原始起点检查:")
for data, label in zip(datasets, labels):
    print(f"{label}: Step={data['Step'].iloc[0]}, Value={data['Value'].iloc[0]}")

# 定义 Savitzky-Golay 滤波器平滑函数
def savitzky_golay_smoothing(data, window_size, poly_order):
    if len(data) < window_size:
        window_size = len(data) if len(data) % 2 == 1 else len(data) - 1
        if window_size < 3:
            return data
    return savgol_filter(data, window_size, poly_order)

# 平滑数据
window_size = 29  # 选择合适的窗口大小，必须是奇数
poly_order = 3    # 多项式阶数

smoothed_datasets = []
for data, label in zip(datasets, labels):
    data_smooth = data.copy()
    
    # 应用平滑
    smoothed_values = savitzky_golay_smoothing(data['Value'], window_size, poly_order)
    
    if SOLUTION == 2:
        # 方案2: 调整平滑后的起点为0
        adjusted_values, offset = adjust_smoothed_start_to_zero(data, smoothed_values)
        data_smooth['smoothed_value'] = adjusted_values
        print(f"{label}: 平滑后起点从 {smoothed_values[0]:.2f} 调整到 0.00 (偏移: {offset:.2f})")
    else:
        data_smooth['smoothed_value'] = smoothed_values
        print(f"{label}: 平滑后起点: {smoothed_values[0]:.2f}")
    
    # 计算标准差
    data_smooth['std'] = data['Value'].rolling(window=window_size, min_periods=1).std()
    
    smoothed_datasets.append(data_smooth)

# 最终验证
print(f"\n最终起点验证:")
for data, label in zip(smoothed_datasets, labels):
    start_step = data['Step'].iloc[0]
    start_value = data['smoothed_value'].iloc[0]
    print(f"{label}: Step={start_step}, 平滑Value={start_value:.6f}")

# 自定义数字格式
def custom_formatter(x, pos):
    return f'{x:,.0f}'

# 绘制平滑后的曲线和置信区间
plt.figure(figsize=(15, 8))

colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']

# 绘制所有曲线
for data, label, color in zip(smoothed_datasets, labels, colors):
    plt.plot(data['Step'], data['smoothed_value'], label=label, color=color, linewidth=2.5)
    plt.fill_between(data['Step'],
                     data['smoothed_value'] - data['std'],
                     data['smoothed_value'] + data['std'],
                     color=color, alpha=0.15)

# 添加起点标记
plt.axhline(y=0, color='red', linestyle=':', alpha=0.8, linewidth=2, 
           label='Zero Start Line')

# 在起点位置添加标记点
for data, color in zip(smoothed_datasets, colors):
    plt.plot(data['Step'].iloc[0], data['smoothed_value'].iloc[0], 'o', 
             color=color, markersize=8, markeredgecolor='white', markeredgewidth=2, zorder=5)

# 添加标题和标签
solution_name = "Zero-Padded Data" if SOLUTION == 1 else "Post-Smoothing Adjustment"
plt.title(f'Average Reward over Time\n(Starting from Zero - {solution_name})', 
          fontsize=16, fontweight='bold')
plt.xlabel('Episodes', fontsize=14)
plt.ylabel('Reward', fontsize=14)
plt.legend(fontsize=12, loc='best')

# 设置x轴和y轴的格式
plt.gca().xaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))
plt.gca().yaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))

# 添加网格
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)

# 确保y轴从0开始显示
all_values = np.concatenate([data['smoothed_value'].values for data in smoothed_datasets])
plt.ylim(min(-100, min(all_values) - 100), max(all_values) + 200)

# 添加说明文本
if SOLUTION == 2:
    textstr = 'All curves adjusted to start from zero\nafter Savitzky-Golay smoothing'
else:
    textstr = 'Zero-padding added before smoothing\nto stabilize starting point'

props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
         verticalalignment='top', bbox=props)

plt.tight_layout()
plt.savefig('奖励图_从0开始.png', dpi=300, bbox_inches='tight')
print(f"\n✅ 从0开始的奖励图已保存为: 奖励图_从0开始.png")
print("📍 现在所有曲线都从0开始了！")
plt.show()
