import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import matplotlib.ticker as ticker
import numpy as np

# 读取本地CSV文件
file_path1 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx'
file_path2 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx'
file_path3 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx'
file_path4 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'

data1 = pd.read_excel(file_path1)
data2 = pd.read_excel(file_path2)
data3 = pd.read_excel(file_path3)
data4 = pd.read_excel(file_path4)

# 数据清理：移除重复的Step=0行，只保留第一个真正的训练数据
def clean_data(data):
    """清理数据，处理重复的Step=0问题"""
    step_zero_rows = data[data['Step'] == 0]
    
    if len(step_zero_rows) > 1:
        data_cleaned = data.copy()
        zero_value_zero_step = data_cleaned[(data_cleaned['Step'] == 0) & (data_cleaned['Value'] == 0)]
        if len(zero_value_zero_step) > 0:
            data_cleaned = data_cleaned.drop(zero_value_zero_step.index[0])
        data_cleaned = data_cleaned.reset_index(drop=True)
        return data_cleaned
    else:
        return data

# 清理所有数据
data1_clean = clean_data(data1)
data2_clean = clean_data(data2)
data3_clean = clean_data(data3)
data4_clean = clean_data(data4)

# 过滤 'Step' 列数值在 10000 之前的数据
data1_clean = data1_clean[data1_clean['Step'] < 10000]
data2_clean = data2_clean[data2_clean['Step'] < 10000]
data3_clean = data3_clean[data3_clean['Step'] < 10000]
data4_clean = data4_clean[data4_clean['Step'] < 10000]

datasets = [data1_clean, data2_clean, data3_clean, data4_clean]
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']

print("原始数据起点:")
for data, label in zip(datasets, labels):
    print(f"{label}: Step={data['Step'].iloc[0]}, Value={data['Value'].iloc[0]:.2f}")

# 定义 Savitzky-Golay 滤波器平滑函数
def savitzky_golay_smoothing(data, window_size, poly_order):
    if len(data) < window_size:
        window_size = len(data) if len(data) % 2 == 1 else len(data) - 1
        if window_size < 3:
            return data
    return savgol_filter(data, window_size, poly_order)

# 先对原始数据进行平滑处理
window_size = 29
poly_order = 3

smoothed_datasets = []
for data in datasets:
    data_smooth = data.copy()
    data_smooth['smoothed_value'] = savitzky_golay_smoothing(data['Value'], window_size, poly_order)
    data_smooth['std'] = data['Value'].rolling(window=window_size, min_periods=1).std()
    smoothed_datasets.append(data_smooth)

print("\n平滑后起点:")
for data, label in zip(smoothed_datasets, labels):
    print(f"{label}: smoothed_value={data['smoothed_value'].iloc[0]:.2f}")

# 统一起点：将所有平滑后的曲线调整到相同起点
TARGET_START_VALUE = 500  # 目标起点值，可以调整

unified_datasets = []
print(f"\n调整到统一起点 {TARGET_START_VALUE}:")

for data, label in zip(smoothed_datasets, labels):
    data_unified = data.copy()
    original_start = data['smoothed_value'].iloc[0]
    offset = TARGET_START_VALUE - original_start
    
    # 调整平滑值
    data_unified['smoothed_value'] = data['smoothed_value'] + offset
    
    print(f"{label}: 原始起点={original_start:.2f}, 偏移量={offset:.2f}, 新起点={data_unified['smoothed_value'].iloc[0]:.2f}")
    unified_datasets.append(data_unified)

# 验证统一后的起点
print(f"\n最终验证 - 统一后起点:")
for data, label in zip(unified_datasets, labels):
    actual_start = data['smoothed_value'].iloc[0]
    print(f"{label}: {actual_start:.6f}")

# 自定义数字格式
def custom_formatter(x, pos):
    return f'{x:,.0f}'

# 绘制图表
plt.figure(figsize=(15, 8))

colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']

# 绘制所有曲线
for data, label, color in zip(unified_datasets, labels, colors):
    plt.plot(data['Step'], data['smoothed_value'], label=label, color=color, linewidth=2)
    plt.fill_between(data['Step'],
                     data['smoothed_value'] - data['std'],
                     data['smoothed_value'] + data['std'],
                     color=color, alpha=0.11)

# 添加起点标记线
plt.axhline(y=TARGET_START_VALUE, color='red', linestyle=':', alpha=0.8, linewidth=1, 
           label=f'Unified Start Point ({TARGET_START_VALUE})')

# 在起点位置添加标记点
for data, color in zip(unified_datasets, colors):
    plt.plot(data['Step'].iloc[0], data['smoothed_value'].iloc[0], 'o', 
             color=color, markersize=8, markeredgecolor='white', markeredgewidth=1)

# 添加标题和标签
plt.title('Average Reward over Time (Unified Starting Point)', fontsize=16, fontweight='bold')
plt.xlabel('Episodes', fontsize=14)
plt.ylabel('Reward (Adjusted to Common Start)', fontsize=14)
plt.legend(fontsize=12, loc='best')

# 设置坐标轴格式
plt.gca().xaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))
plt.gca().yaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))

# 添加网格
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)

# 设置坐标轴范围，确保起点清晰可见
all_steps = np.concatenate([data['Step'].values for data in unified_datasets])
all_values = np.concatenate([data['smoothed_value'].values for data in unified_datasets])

plt.xlim(min(all_steps) - 50, max(all_steps) + 50)
plt.ylim(min(all_values) - 100, max(all_values) + 100)

plt.tight_layout()
plt.savefig('奖励图_完全统一起点.png', dpi=300, bbox_inches='tight')
print(f"\n✅ 完全统一起点图表已保存为: 奖励图_完全统一起点.png")
print(f"📍 所有算法现在都从 {TARGET_START_VALUE} 这个点开始")
plt.show()
