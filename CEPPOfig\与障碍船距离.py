import pandas as pd
import matplotlib.pyplot as plt

# ===== Excel 文件路径 =====
file_paths = [
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/CEPPO.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/线性熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/固定熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/PPO.xlsx"
]

# ===== 图例标签 =====
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']

# ===== 颜色设置 =====
colors = ["#e76f51", "#e8c56a", "#299d91", "#8bb17b"]

# ===== 创建图形 =====
plt.figure(figsize=(10, 6))

# ===== 遍历文件并绘图 =====
for file_path, label, color in zip(file_paths, labels, colors):
    df = pd.read_excel(file_path)

    # 第 7 列为与障碍船的距离（单位 cm，需转为 m）
    raw_distance_cm = df.iloc[:, 6]
    raw_distance_m = raw_distance_cm / 100  # 转为米
    clipped_distance = raw_distance_m.clip(upper=200)

    # 时间轴（每帧 0.3 秒）
    time = df.index * 0.3

    # ===== 筛选时间范围为 [1, 300] 秒的数据 =====
    mask = (time >= 50) & (time <= 200)
    time_filtered = time[mask]
    distance_filtered = clipped_distance[mask]

    # 绘图
    plt.plot(time_filtered, distance_filtered, label=label, color=color)

# ===== 图形设置 =====
plt.xlabel("Time (s)")
plt.ylabel("Distance to Obstacle Ship (m)")
plt.title("Distance to Obstacle Ship (1~300s, Capped at 200m)")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
