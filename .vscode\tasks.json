{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: gcc.exe 生成活动文件", "command": "D:/mingw64/bin/gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "D:/mingw64/bin"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "编译器: D:/mingw64/bin/gcc.exe"}, {"type": "shell", "label": "启动 GPT Academic", "command": "${workspaceFolder}/.venv/Scripts/python.exe", "args": ["main.py"], "options": {"cwd": "${workspaceFolder}/gpt_academic-master"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "启动 GPT Academic 学术优化工具（使用虚拟环境）"}, {"type": "shell", "label": "启动 ChatPaper", "command": "${workspaceFolder}/.venv/Scripts/python.exe", "args": ["HuggingFaceDeploy/app.py"], "options": {"cwd": "${workspaceFolder}/ChatPaper-main"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "启动 ChatPaper 论文分析工具"}, {"type": "shell", "label": "安装依赖", "command": "${workspaceFolder}/.venv/Scripts/pip.exe", "args": ["install", "-r", "requirements.txt"], "options": {"cwd": "${workspaceFolder}/gpt_academic-master"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "安装 GPT Academic 项目依赖"}]}