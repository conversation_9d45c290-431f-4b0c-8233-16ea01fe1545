import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import matplotlib.ticker as ticker
import numpy as np

# 读取本地CSV文件
file_path1 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx'
file_path2 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx'
file_path3 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx'
file_path4 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'

data1 = pd.read_excel(file_path1)
data2 = pd.read_excel(file_path2)
data3 = pd.read_excel(file_path3)
data4 = pd.read_excel(file_path4)

# 数据清理：移除重复的Step=0行，只保留第一个真正的训练数据
def clean_data(data):
    """清理数据，处理重复的Step=0问题"""
    # 找到Step=0的所有行
    step_zero_rows = data[data['Step'] == 0]
    
    if len(step_zero_rows) > 1:
        # 如果有多个Step=0的行，移除Value=0的初始化行
        data_cleaned = data.copy()
        
        # 找到Value=0且Step=0的行并删除
        zero_value_zero_step = data_cleaned[(data_cleaned['Step'] == 0) & (data_cleaned['Value'] == 0)]
        if len(zero_value_zero_step) > 0:
            data_cleaned = data_cleaned.drop(zero_value_zero_step.index[0])
            
        # 重新索引
        data_cleaned = data_cleaned.reset_index(drop=True)
        return data_cleaned
    else:
        return data

# 清理所有数据
data1_clean = clean_data(data1)
data2_clean = clean_data(data2)
data3_clean = clean_data(data3)
data4_clean = clean_data(data4)

# 过滤 'Step' 列数值在 10000 之前的数据
data1_clean = data1_clean[data1_clean['Step'] < 10000]
data2_clean = data2_clean[data2_clean['Step'] < 10000]
data3_clean = data3_clean[data3_clean['Step'] < 10000]
data4_clean = data4_clean[data4_clean['Step'] < 10000]

# 统一起点方法1：所有算法都从0开始（添加统一起点）
def normalize_start_point(datasets, labels, target_start_value=0):
    """将所有数据集的起点统一到指定值"""
    normalized_datasets = []
    
    print("原始起点值:")
    for data, label in zip(datasets, labels):
        original_start = data['Value'].iloc[0]
        print(f"{label}: {original_start:.2f}")
    
    # 方法1：平移所有数据，使起点都为target_start_value
    for data, label in zip(datasets, labels):
        data_norm = data.copy()
        original_start = data['Value'].iloc[0]
        offset = target_start_value - original_start
        data_norm['Value'] = data['Value'] + offset
        normalized_datasets.append(data_norm)
        print(f"{label} 平移了 {offset:.2f}")
    
    return normalized_datasets

# 统一起点方法2：使用相对变化（从第一个值开始的增量）
def relative_change_normalization(datasets, labels, base_value=0):
    """将所有数据转换为相对于起点的变化量"""
    normalized_datasets = []
    
    print("\n使用相对变化归一化:")
    for data, label in zip(datasets, labels):
        data_norm = data.copy()
        start_value = data['Value'].iloc[0]
        # 计算相对于起点的变化
        data_norm['Value'] = data['Value'] - start_value + base_value
        normalized_datasets.append(data_norm)
        print(f"{label}: 起点从 {start_value:.2f} 归一化到 {base_value}")
    
    return normalized_datasets

# 选择归一化方法
print("选择归一化方法:")
print("1. 平移法：保持数据的绝对值关系，所有曲线平移到相同起点")
print("2. 相对变化法：显示相对于各自起点的改进程度")

# 这里我们使用相对变化法，您可以根据需要选择
use_relative = True  # 设为False使用平移法

if use_relative:
    # 使用相对变化法，所有算法从相同起点开始
    datasets_normalized = relative_change_normalization([data1_clean, data2_clean, data3_clean, data4_clean], 
                                                       ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO'],
                                                       base_value=500)  # 可以调整这个基准值
else:
    # 使用平移法
    datasets_normalized = normalize_start_point([data1_clean, data2_clean, data3_clean, data4_clean], 
                                              ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO'],
                                              target_start_value=500)  # 可以调整这个目标起点

data1_norm, data2_norm, data3_norm, data4_norm = datasets_normalized

# 定义 Savitzky-Golay 滤波器平滑函数
def savitzky_golay_smoothing(data, window_size, poly_order):
    if len(data) < window_size:
        window_size = len(data) if len(data) % 2 == 1 else len(data) - 1
        if window_size < 3:
            return data
    return savgol_filter(data, window_size, poly_order)

# 平滑数据
window_size = 29  # 选择合适的窗口大小，必须是奇数
poly_order = 3    # 多项式阶数

data1_norm['smoothed_value'] = savitzky_golay_smoothing(data1_norm['Value'], window_size, poly_order)
data2_norm['smoothed_value'] = savitzky_golay_smoothing(data2_norm['Value'], window_size, poly_order)
data3_norm['smoothed_value'] = savitzky_golay_smoothing(data3_norm['Value'], window_size, poly_order)
data4_norm['smoothed_value'] = savitzky_golay_smoothing(data4_norm['Value'], window_size, poly_order)

# 计算方差或置信区间
data1_norm['std'] = data1_norm['Value'].rolling(window=window_size, min_periods=1).std()
data2_norm['std'] = data2_norm['Value'].rolling(window=window_size, min_periods=1).std()
data3_norm['std'] = data3_norm['Value'].rolling(window=window_size, min_periods=1).std()
data4_norm['std'] = data4_norm['Value'].rolling(window=window_size, min_periods=1).std()

# 自定义数字格式
def custom_formatter(x, pos):
    return f'{x:,.0f}'

# 绘制平滑后的曲线和置信区间
plt.figure(figsize=(15, 8))

colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']
datasets_final = [data1_norm, data2_norm, data3_norm, data4_norm]

# 验证起点是否统一
print(f"\n归一化后各算法起点Value:")
for data, label in zip(datasets_final, labels):
    start_value = data['smoothed_value'].iloc[0]
    print(f"{label}: {start_value:.2f}")

# 绘制所有曲线
for data, label, color in zip(datasets_final, labels, colors):
    plt.plot(data['Step'], data['smoothed_value'], label=label, color=color, linewidth=2)
    plt.fill_between(data['Step'],
                     data['smoothed_value'] - data['std'],
                     data['smoothed_value'] + data['std'],
                     color=color, alpha=0.11)

# 添加标题和标签
if use_relative:
    plt.title('Average Reward over Time (Normalized to Same Starting Point)', fontsize=16)
    plt.ylabel('Normalized Reward (Relative to Start)', fontsize=14)
else:
    plt.title('Average Reward over Time (Shifted to Same Starting Point)', fontsize=16)
    plt.ylabel('Shifted Reward', fontsize=14)

plt.xlabel('Episodes', fontsize=14)
plt.legend(fontsize=12)

# 设置x轴和y轴的格式
plt.gca().xaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))
plt.gca().yaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))

# 添加网格
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)

# 添加起点标记
start_y = datasets_final[0]['smoothed_value'].iloc[0]
plt.axhline(y=start_y, color='red', linestyle=':', alpha=0.7, label=f'Common Start Point ({start_y:.0f})')

plt.tight_layout()
plt.savefig('奖励图_统一起点.png', dpi=300, bbox_inches='tight')
print("统一起点图表已保存为: 奖励图_统一起点.png")
plt.show()
