import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

# ===== 两条轨迹文件路径：CEPPO 与 预设轨迹 =====
trajectory_files = [
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/固定熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/预设轨迹.xlsx"
]

# ===== 图例标签 =====
labels = ["Improved PPO by Entropy", "Preset Trajectory"]

# ===== 每条轨迹的颜色 =====
colors = ["#9b59b6", "green"]

# ===== 图像中起点和终点的像素坐标点 =====
pixel_start = np.array([2362.04, 1123.67])
pixel_end = np.array([466.49, 220.54])

# ===== 读取背景图像 =====
img = mpimg.imread("C:/Users/<USER>/Desktop/实验数据/环境/无障碍环境.png")

# ===== 创建画布 =====
fig, ax = plt.subplots(figsize=(12, 8))
ax.imshow(img, zorder=0)

# ===== 绘制轨迹 =====
for i, file_path in enumerate(trajectory_files):
    df = pd.read_excel(file_path, header=None)
    x_world = df.iloc[:, 0].values
    y_world = df.iloc[:, 1].values

    world_start = np.array([x_world[0], y_world[0]])
    world_end = np.array([x_world[-1], y_world[-1]])

    vec_world = world_end - world_start
    vec_pixel = pixel_end - pixel_start
    angle_world = np.arctan2(vec_world[1], vec_world[0])
    angle_pixel = np.arctan2(vec_pixel[1], vec_pixel[0])
    theta = angle_pixel - angle_world
    scale = np.linalg.norm(vec_pixel) / np.linalg.norm(vec_world)

    R = np.array([
        [np.cos(theta), -np.sin(theta)],
        [np.sin(theta),  np.cos(theta)]
    ])

    transformed_points = []
    for j in range(len(x_world)):
        point = np.array([x_world[j], y_world[j]]) - world_start
        point = R @ point * scale
        point = point + pixel_start
        transformed_points.append(point)

    transformed_points = np.array(transformed_points)
    x_img = transformed_points[:, 0]
    y_img = transformed_points[:, 1]

    # 第二条轨迹使用虚线、加粗
    linestyle = '--' if i == 1 else '-'
    linewidth = 2.5 if i == 1 else 1.5
    zorder = 1 if i == 1 else 2

    ax.plot(
        x_img, y_img,
        linestyle=linestyle,
        color=colors[i],
        linewidth=linewidth,
        label=labels[i],
        zorder=zorder
    )

# ===== 添加图例 =====
ax.legend(loc='upper right', fontsize=12, frameon=True)

# ===== 移除坐标轴 =====
ax.axis('off')
plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
plt.margins(0)
plt.show()
