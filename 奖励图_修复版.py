import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import matplotlib.ticker as ticker
import numpy as np

# 读取本地CSV文件
file_path1 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx'
file_path2 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx'
file_path3 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx'
file_path4 = r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'

data1 = pd.read_excel(file_path1)
data2 = pd.read_excel(file_path2)
data3 = pd.read_excel(file_path3)
data4 = pd.read_excel(file_path4)

print("原始数据前5行:")
print("CEPPO:", data1[['Step', 'Value']].head())
print("Linear Entropy:", data2[['Step', 'Value']].head())

# 数据清理：移除重复的Step=0行，只保留第一个真正的训练数据
def clean_data(data):
    """清理数据，处理重复的Step=0问题"""
    # 找到Step=0的所有行
    step_zero_rows = data[data['Step'] == 0]
    
    if len(step_zero_rows) > 1:
        # 如果有多个Step=0的行，移除Value=0的初始化行
        # 保留Value不为0的第一个训练结果
        data_cleaned = data.copy()
        
        # 找到Value=0且Step=0的行并删除
        zero_value_zero_step = data_cleaned[(data_cleaned['Step'] == 0) & (data_cleaned['Value'] == 0)]
        if len(zero_value_zero_step) > 0:
            data_cleaned = data_cleaned.drop(zero_value_zero_step.index[0])
            
        # 重新索引
        data_cleaned = data_cleaned.reset_index(drop=True)
        return data_cleaned
    else:
        return data

# 清理所有数据
data1_clean = clean_data(data1)
data2_clean = clean_data(data2)
data3_clean = clean_data(data3)
data4_clean = clean_data(data4)

print("\n清理后数据前5行:")
print("CEPPO:", data1_clean[['Step', 'Value']].head())
print("Linear Entropy:", data2_clean[['Step', 'Value']].head())

# 过滤 'Step' 列数值在 10000 之前的数据
data1_clean = data1_clean[data1_clean['Step'] < 10000]
data2_clean = data2_clean[data2_clean['Step'] < 10000]
data3_clean = data3_clean[data3_clean['Step'] < 10000]
data4_clean = data4_clean[data4_clean['Step'] < 10000]

# 数据对齐：确保所有数据从相同的Step开始
def align_data_start(datasets, labels):
    """对齐数据起点"""
    # 找到所有数据集的最小Step值
    min_steps = [data['Step'].min() for data in datasets]
    max_min_step = max(min_steps)  # 找到最大的最小值
    
    print(f"\n各算法最小Step值: {dict(zip(labels, min_steps))}")
    print(f"统一起点设为: {max_min_step}")
    
    # 将所有数据集的Step都从这个值开始
    aligned_datasets = []
    for data in datasets:
        aligned_data = data[data['Step'] >= max_min_step].copy()
        aligned_datasets.append(aligned_data)
    
    return aligned_datasets

# 对齐数据
labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']
aligned_datasets = align_data_start([data1_clean, data2_clean, data3_clean, data4_clean], labels)
data1_aligned, data2_aligned, data3_aligned, data4_aligned = aligned_datasets

# 定义 Savitzky-Golay 滤波器平滑函数
def savitzky_golay_smoothing(data, window_size, poly_order):
    if len(data) < window_size:
        window_size = len(data) if len(data) % 2 == 1 else len(data) - 1
        if window_size < 3:
            return data
    return savgol_filter(data, window_size, poly_order)

# 平滑数据
window_size = 29  # 选择合适的窗口大小，必须是奇数
poly_order = 3    # 多项式阶数

# 检查数据长度并调整窗口大小
for i, (data, label) in enumerate(zip(aligned_datasets, labels)):
    if len(data) < window_size:
        print(f"警告: {label} 数据长度 {len(data)} 小于窗口大小 {window_size}")

data1_aligned['smoothed_value'] = savitzky_golay_smoothing(data1_aligned['Value'], window_size, poly_order)
data2_aligned['smoothed_value'] = savitzky_golay_smoothing(data2_aligned['Value'], window_size, poly_order)
data3_aligned['smoothed_value'] = savitzky_golay_smoothing(data3_aligned['Value'], window_size, poly_order)
data4_aligned['smoothed_value'] = savitzky_golay_smoothing(data4_aligned['Value'], window_size, poly_order)

# 计算方差或置信区间
data1_aligned['std'] = data1_aligned['Value'].rolling(window=window_size, min_periods=1).std()
data2_aligned['std'] = data2_aligned['Value'].rolling(window=window_size, min_periods=1).std()
data3_aligned['std'] = data3_aligned['Value'].rolling(window=window_size, min_periods=1).std()
data4_aligned['std'] = data4_aligned['Value'].rolling(window=window_size, min_periods=1).std()

# 自定义数字格式
def custom_formatter(x, pos):
    return f'{x:,.0f}'

# 绘制平滑后的曲线和置信区间
plt.figure(figsize=(15, 8))

colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']
datasets_final = [data1_aligned, data2_aligned, data3_aligned, data4_aligned]

# 绘制所有曲线
for data, label, color in zip(datasets_final, labels, colors):
    plt.plot(data['Step'], data['smoothed_value'], label=label, color=color, linewidth=2)
    plt.fill_between(data['Step'],
                     data['smoothed_value'] - data['std'],
                     data['smoothed_value'] + data['std'],
                     color=color, alpha=0.11)

# 添加标题和标签
plt.title('Average Reward over Time (Aligned Start Points)', fontsize=16)
plt.xlabel('Episodes', fontsize=14)
plt.ylabel('Reward', fontsize=14)
plt.legend(fontsize=12)

# 设置x轴和y轴的格式
plt.gca().xaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))
plt.gca().yaxis.set_major_formatter(ticker.FuncFormatter(custom_formatter))

# 添加网格
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)

# 显示起点信息
start_values = [data['smoothed_value'].iloc[0] for data in datasets_final]
print(f"\n对齐后各算法起点Value: {dict(zip(labels, start_values))}")

plt.tight_layout()
plt.savefig('奖励图_修复版.png', dpi=300, bbox_inches='tight')
print("修复版图表已保存为: 奖励图_修复版.png")
plt.show()
