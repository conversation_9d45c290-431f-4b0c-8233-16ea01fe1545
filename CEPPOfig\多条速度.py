import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter  # 引入平滑滤波器

# ===== Excel 文件路径（请根据实际路径修改）=====
excel_files = [
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/CEPPO.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/线性熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/固定熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/经典船舶会遇/交叉相遇/PPO.xlsx"
]

# ===== 算法标签（用于图例）=====
labels = [
    "CEPPO",
    "Improved PPO by Linear Entropy",
    "Improved PPO by Entropy",
    "Classic PPO"
]

# ===== 每种算法对应的颜色 =====
colors = ["#e76f51", "#e8c56a", "#299d91", "#8bb17b"]

# ===== 创建图形 =====
plt.figure(figsize=(10, 6))

# ===== 遍历每个算法的数据文件 =====
for i in range(len(excel_files)):
    df = pd.read_excel(excel_files[i])

    # 提取速度并缩放（除以100）
    x_speed = df.iloc[:, 2] / 100
    raw_y_speed = df.iloc[:, 3] / 100

    # 对 y_speed 应用 Savitzky-Golay 平滑滤波
    y_speed = savgol_filter(raw_y_speed, window_length=11, polyorder=2)

    # 创建时间轴（每帧 0.3 秒）
    time = df.index * 0.3

    # 绘制 x轴速度（实线）- 原始
    plt.plot(time, x_speed, color=colors[i], linestyle='--', linewidth=2, label=f"{labels[i]} - USV-u Speed")

    # 绘制 y轴速度（虚线）- 已平滑
    plt.plot(time, y_speed, color=colors[i], linestyle='-', linewidth=2, label=f"{labels[i]} - USV-v Speed")

# ===== 图形设置 =====
plt.title("USV-u and USV-v Axis Speeds for Different Algorithms")
plt.xlabel("Time (s)")
plt.ylabel("Speed (m/s)")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
