# -*- coding: utf-8 -*-
'''
简单左转遮蔽测试
验证"禁止左转"的简单动作遮蔽效果
'''

import numpy as np
import torch
import torch.nn as nn

def apply_simple_left_turn_masking(act_logits):
    """
    简单的左转遮蔽：禁止左转动作
    
    假设动作空间为：[大幅左转, 小幅左转, 保持航向, 小幅右转, 大幅右转]
    对应索引：        [0,      1,      2,      3,      4]
    
    参数:
    - act_logits: 动作对数概率 [batch_size, 5]
    
    返回:
    - masked_logits: 遮蔽后的对数概率
    """
    masked_logits = act_logits.clone()
    
    # 禁止左转动作（索引0和1）
    masked_logits[:, 0] = -1e10  # 大幅左转
    masked_logits[:, 1] = -1e10  # 小幅左转
    
    return masked_logits

def test_simple_masking():
    """测试简单的左转遮蔽效果"""
    
    print("=== 简单左转遮蔽测试 ===\n")
    
    action_names = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"]
    
    # 创建模拟的策略网络输出
    batch_size = 4
    action_dim = 5
    
    # 生成随机的动作对数概率
    torch.manual_seed(42)  # 固定随机种子以便复现
    raw_logits = torch.randn(batch_size, action_dim)
    
    print("1. 原始策略网络输出:")
    print("-" * 40)
    
    # 计算原始概率分布
    raw_probs = torch.softmax(raw_logits, dim=-1)
    
    for i in range(batch_size):
        print(f"样本 {i+1}:")
        for j, (name, prob) in enumerate(zip(action_names, raw_probs[i])):
            print(f"  {name}: {prob:.3f}")
        print()
    
    print("2. 应用左转遮蔽后:")
    print("-" * 40)
    
    # 应用左转遮蔽
    masked_logits = apply_simple_left_turn_masking(raw_logits)
    masked_probs = torch.softmax(masked_logits, dim=-1)
    
    for i in range(batch_size):
        print(f"样本 {i+1}:")
        for j, (name, prob) in enumerate(zip(action_names, masked_probs[i])):
            status = "❌" if j in [0, 1] else "✅"
            print(f"  {status} {name}: {prob:.3f}")
        print()
    
    print("3. 动作选择对比:")
    print("-" * 40)
    
    # 对比动作选择
    for i in range(batch_size):
        # 原始动作选择
        raw_action = torch.argmax(raw_probs[i]).item()
        raw_action_name = action_names[raw_action]
        
        # 遮蔽后动作选择
        masked_action = torch.argmax(masked_probs[i]).item()
        masked_action_name = action_names[masked_action]
        
        print(f"样本 {i+1}:")
        print(f"  原始选择: {raw_action_name}")
        print(f"  遮蔽后选择: {masked_action_name}")
        
        if raw_action in [0, 1] and masked_action not in [0, 1]:
            print(f"  ✅ 成功阻止左转，改为{masked_action_name}")
        elif raw_action not in [0, 1]:
            print(f"  ✅ 原本就是合规动作")
        print()
    
    return True

def test_training_simulation():
    """模拟训练过程中的效果"""
    
    print("4. 训练效果模拟:")
    print("-" * 40)
    
    action_names = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"]
    
    # 统计数据
    stats = {
        "without_masking": {"left_turns": 0, "total": 0},
        "with_masking": {"left_turns": 0, "total": 0}
    }
    
    num_steps = 1000
    
    for step in range(num_steps):
        # 生成随机策略输出
        logits = torch.randn(1, 5)
        
        # 不使用遮蔽的动作选择
        raw_probs = torch.softmax(logits, dim=-1)
        raw_action = torch.multinomial(raw_probs, 1).item()
        
        stats["without_masking"]["total"] += 1
        if raw_action in [0, 1]:  # 左转动作
            stats["without_masking"]["left_turns"] += 1
        
        # 使用遮蔽的动作选择
        masked_logits = apply_simple_left_turn_masking(logits)
        masked_probs = torch.softmax(masked_logits, dim=-1)
        masked_action = torch.multinomial(masked_probs, 1).item()
        
        stats["with_masking"]["total"] += 1
        if masked_action in [0, 1]:  # 左转动作
            stats["with_masking"]["left_turns"] += 1
    
    # 计算左转率
    left_turn_rate_without = (stats["without_masking"]["left_turns"] / 
                             stats["without_masking"]["total"]) * 100
    left_turn_rate_with = (stats["with_masking"]["left_turns"] / 
                          stats["with_masking"]["total"]) * 100
    
    print(f"模拟 {num_steps} 步动作选择:")
    print(f"\n不使用遮蔽:")
    print(f"  左转次数: {stats['without_masking']['left_turns']}")
    print(f"  左转率: {left_turn_rate_without:.2f}%")
    
    print(f"\n使用左转遮蔽:")
    print(f"  左转次数: {stats['with_masking']['left_turns']}")
    print(f"  左转率: {left_turn_rate_with:.2f}%")
    
    reduction = ((left_turn_rate_without - left_turn_rate_with) / 
                left_turn_rate_without) * 100 if left_turn_rate_without > 0 else 100
    print(f"\n左转率减少: {reduction:.1f}%")

def demonstrate_reward_function_integration():
    """演示如何与奖励函数集成"""
    
    print("\n5. 与奖励函数集成示例:")
    print("-" * 40)
    
    def calculate_reward_with_colreg_rules(action, relative_bearing, distance):
        """
        示例奖励函数：结合COLREG规则
        
        参数:
        - action: 执行的动作 (0-4)
        - relative_bearing: 相对方位角 (0-360)
        - distance: 距离
        """
        action_names = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"]
        
        base_reward = 0.1
        
        # 基本的避碰奖励
        if distance > 2.0:
            base_reward += 0.5  # 保持安全距离
        
        # COLREG规则奖励
        if 350 <= relative_bearing <= 360 or 0 <= relative_bearing <= 10:
            # 对遇情况 (Rule 14)
            if action in [3, 4]:  # 右转
                base_reward += 1.0
                print(f"  ✅ 对遇情况正确右转: {action_names[action]} (+1.0)")
            elif action in [0, 1]:  # 左转 (但由于遮蔽，这种情况不会发生)
                base_reward -= 2.0
                print(f"  ❌ 对遇情况错误左转: {action_names[action]} (-2.0)")
            else:  # 保持航向
                base_reward -= 0.5
                print(f"  ⚠️ 对遇情况保持航向: {action_names[action]} (-0.5)")
                
        elif 5 <= relative_bearing <= 112.5:
            # 交叉相遇让路船 (Rule 15)
            if action in [3, 4]:  # 右转
                base_reward += 1.0
                print(f"  ✅ 让路船正确右转: {action_names[action]} (+1.0)")
            elif action in [0, 1]:  # 左转
                base_reward -= 2.0
                print(f"  ❌ 让路船错误左转: {action_names[action]} (-2.0)")
            else:  # 保持航向
                base_reward -= 1.0
                print(f"  ❌ 让路船不应保持航向: {action_names[action]} (-1.0)")
        
        # 更多规则...
        
        return base_reward
    
    # 测试几个场景
    test_scenarios = [
        (0, "对遇情况"),
        (45, "交叉相遇(让路)"),
        (300, "交叉相遇(直行)"),
    ]
    
    for bearing, description in test_scenarios:
        print(f"\n场景: {description} (方位角: {bearing}°)")
        
        # 模拟策略输出
        logits = torch.tensor([[0.5, 0.3, 0.1, 0.8, 0.6]])  # 偏向右转的输出
        
        # 应用左转遮蔽
        masked_logits = apply_simple_left_turn_masking(logits)
        masked_probs = torch.softmax(masked_logits, dim=-1)
        
        # 选择动作
        action = torch.argmax(masked_probs).item()
        action_name = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"][action]
        
        print(f"选择动作: {action_name}")
        
        # 计算奖励
        reward = calculate_reward_with_colreg_rules(action, bearing, 3.0)
        print(f"总奖励: {reward:.1f}")

if __name__ == "__main__":
    # 运行所有测试
    success = test_simple_masking()
    
    if success:
        test_training_simulation()
        demonstrate_reward_function_integration()
        
        print(f"\n🎉 简单左转遮蔽测试完成!")
        print(f"\n💡 实现要点:")
        print(f"- 算法层面: 简单禁止左转动作 (索引0和1)")
        print(f"- 奖励函数: 实现详细的COLREG规则逻辑")
        print(f"- 接口保持: 不改变输入输出结构")
        print(f"- 效果显著: 左转率从~40%降到0%")
