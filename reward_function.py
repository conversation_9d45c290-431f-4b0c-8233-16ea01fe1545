"""
CEPPO船舶避碰奖励函数设计
基于认知熵的近端策略优化算法的多层次奖励机制

该实现结合了Unreal Engine蓝图奖励系统和Python强化学习奖励函数
包含路径跟踪、碰撞避免、COLREGs合规性等多重奖励机制

作者：基于您的CEPPO研究项目设计
日期：2025-07-04
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Optional

class ShipCollisionAvoidanceReward:
    """
    船舶避碰强化学习奖励函数类
    
    该类实现了多层次的奖励机制，包括：
    1. 安全性奖励（碰撞风险评估）
    2. 效率性奖励（路径优化）
    3. COLREGs合规性奖励（国际海上避碰规则）
    4. 动态调整机制（基于认知熵）
    """
    
    def __init__(self, 
                 ship_length: float = 100.0,
                 safety_radius_multiplier: float = 1.5,
                 target_speed: float = 10.0,
                 colregs_weight: float = 1.0,
                 safety_weight: float = 2.0,
                 efficiency_weight: float = 0.5):
        """
        初始化奖励函数参数
        
        Args:
            ship_length: 船舶长度 (米)
            safety_radius_multiplier: 安全半径倍数
            target_speed: 目标航行速度 (节)
            colregs_weight: COLREGs合规性权重
            safety_weight: 安全性权重
            efficiency_weight: 效率性权重
        """
        self.ship_length = ship_length
        self.safety_radius = ship_length * safety_radius_multiplier
        self.target_speed = target_speed
        self.colregs_weight = colregs_weight
        self.safety_weight = safety_weight
        self.efficiency_weight = efficiency_weight
        
        # 奖励函数参数
        self.collision_penalty = -1000.0  # 碰撞惩罚
        self.goal_reward = 100.0          # 到达目标奖励
        self.step_penalty = -0.1          # 步数惩罚
        
    def calculate_dcpa_tcpa(self, 
                           own_pos: np.ndarray, 
                           own_vel: np.ndarray,
                           target_pos: np.ndarray, 
                           target_vel: np.ndarray) -> Tuple[float, float]:
        """
        计算最近接近点距离(DCPA)和到达最近接近点时间(TCPA)
        
        Args:
            own_pos: 本船位置 [x, y]
            own_vel: 本船速度 [vx, vy]
            target_pos: 目标船位置 [x, y]
            target_vel: 目标船速度 [vx, vy]
            
        Returns:
            Tuple[DCPA, TCPA]
        """
        # 相对位置和速度
        rel_pos = target_pos - own_pos
        rel_vel = target_vel - own_vel
        
        # 当前距离
        distance = np.linalg.norm(rel_pos)
        
        # 相对速度大小
        rel_speed = np.linalg.norm(rel_vel)
        
        if rel_speed < 1e-6:  # 相对速度接近零
            return distance, float('inf')
        
        # TCPA计算
        tcpa = -np.dot(rel_pos, rel_vel) / (rel_speed ** 2)
        
        if tcpa < 0:  # 目标船正在远离
            tcpa = 0
            
        # DCPA计算
        closest_rel_pos = rel_pos + rel_vel * tcpa
        dcpa = np.linalg.norm(closest_rel_pos)
        
        return dcpa, tcpa
    
    def calculate_collision_risk(self, dcpa: float, tcpa: float) -> float:
        """
        基于DCPA和TCPA计算碰撞风险
        
        Args:
            dcpa: 最近接近点距离
            tcpa: 到达最近接近点时间
            
        Returns:
            碰撞风险值 [0, 1]
        """
        # 参数设置
        c1 = self.safety_radius / 2.0  # DCPA缩放参数
        c2 = 300.0  # TCPA缩放参数（秒）
        
        # 碰撞风险计算（基于您论文中的公式）
        if tcpa <= 0:
            return 0.0
            
        risk_dcpa = math.exp(-dcpa / c1)
        risk_tcpa = math.exp(-tcpa / c2)
        
        collision_risk = risk_dcpa * risk_tcpa
        
        return min(collision_risk, 1.0)
    
    def get_encounter_type(self, 
                          own_heading: float, 
                          target_bearing: float) -> str:
        """
        根据COLREGs确定会遇类型
        
        Args:
            own_heading: 本船航向 (度)
            target_bearing: 目标船相对方位 (度)
            
        Returns:
            会遇类型: 'head_on', 'crossing_starboard', 'crossing_port', 'overtaking'
        """
        # 标准化角度到[0, 360)
        bearing = target_bearing % 360
        
        # 根据COLREGs规则判断会遇类型
        if (bearing >= 355 or bearing <= 5) or (bearing >= 175 and bearing <= 185):
            return 'head_on'
        elif bearing >= 5 and bearing <= 112.5:
            return 'crossing_starboard'
        elif bearing >= 247.5 and bearing <= 355:
            return 'crossing_port'
        elif bearing > 112.5 and bearing < 247.5:
            return 'overtaking'
        else:
            return 'unknown'
    
    def calculate_colregs_compliance(self, 
                                   encounter_type: str,
                                   own_action: Dict,
                                   collision_risk: float) -> float:
        """
        计算COLREGs合规性奖励
        
        Args:
            encounter_type: 会遇类型
            own_action: 本船动作 {'rudder_angle': float, 'thrust': float}
            collision_risk: 当前碰撞风险
            
        Returns:
            COLREGs合规性奖励
        """
        compliance_reward = 0.0
        
        # 如果碰撞风险较低，不需要避让动作
        if collision_risk < 0.1:
            return 0.0
        
        rudder_angle = own_action.get('rudder_angle', 0.0)
        
        if encounter_type == 'head_on':
            # 对遇局面：双方向右转向
            if rudder_angle > 5.0:  # 向右转
                compliance_reward = 10.0
            elif rudder_angle < -5.0:  # 向左转（违规）
                compliance_reward = -20.0
                
        elif encounter_type == 'crossing_starboard':
            # 右舷交叉：本船为让路船，应避让
            if rudder_angle > 5.0:  # 向右转避让
                compliance_reward = 15.0
            elif abs(rudder_angle) < 5.0:  # 保持航向（违规）
                compliance_reward = -15.0
                
        elif encounter_type == 'crossing_port':
            # 左舷交叉：本船为直航船，应保持航向
            if abs(rudder_angle) < 5.0:  # 保持航向
                compliance_reward = 10.0
            else:  # 不必要的避让
                compliance_reward = -5.0
                
        elif encounter_type == 'overtaking':
            # 追越局面：追越船应避让
            if abs(rudder_angle) > 5.0:  # 进行避让
                compliance_reward = 10.0
            else:  # 未避让
                compliance_reward = -10.0
        
        return compliance_reward * self.colregs_weight
    
    def calculate_safety_reward(self, 
                              collision_risks: List[float],
                              min_distance: float) -> float:
        """
        计算安全性奖励
        
        Args:
            collision_risks: 与所有目标船的碰撞风险列表
            min_distance: 与最近船舶的距离
            
        Returns:
            安全性奖励
        """
        # 最大碰撞风险
        max_risk = max(collision_risks) if collision_risks else 0.0
        
        # 安全性奖励计算
        if max_risk > 0.8:  # 高风险
            safety_reward = -50.0 * max_risk
        elif max_risk > 0.5:  # 中等风险
            safety_reward = -20.0 * max_risk
        elif max_risk > 0.2:  # 低风险
            safety_reward = -5.0 * max_risk
        else:  # 安全
            safety_reward = 5.0
        
        # 距离奖励
        if min_distance < self.safety_radius:
            distance_penalty = -30.0 * (self.safety_radius - min_distance) / self.safety_radius
            safety_reward += distance_penalty
        
        return safety_reward * self.safety_weight
    
    def calculate_efficiency_reward(self, 
                                  current_pos: np.ndarray,
                                  target_pos: np.ndarray,
                                  previous_pos: np.ndarray,
                                  current_speed: float) -> float:
        """
        计算效率性奖励
        
        Args:
            current_pos: 当前位置
            target_pos: 目标位置
            previous_pos: 上一步位置
            current_speed: 当前速度
            
        Returns:
            效率性奖励
        """
        # 距离目标的进展
        current_distance = np.linalg.norm(target_pos - current_pos)
        previous_distance = np.linalg.norm(target_pos - previous_pos)
        progress = previous_distance - current_distance
        
        # 进展奖励
        progress_reward = progress * 2.0
        
        # 速度奖励（鼓励保持目标速度）
        speed_diff = abs(current_speed - self.target_speed)
        speed_reward = -speed_diff * 0.5
        
        # 航向奖励（鼓励朝向目标）
        direction_to_target = target_pos - current_pos
        if np.linalg.norm(direction_to_target) > 0:
            direction_to_target = direction_to_target / np.linalg.norm(direction_to_target)
            movement = current_pos - previous_pos
            if np.linalg.norm(movement) > 0:
                movement = movement / np.linalg.norm(movement)
                heading_reward = np.dot(direction_to_target, movement) * 2.0
            else:
                heading_reward = 0.0
        else:
            heading_reward = 0.0
        
        efficiency_reward = progress_reward + speed_reward + heading_reward
        
        return efficiency_reward * self.efficiency_weight

    def calculate_cognitive_entropy_factor(self,
                                         training_step: int,
                                         model_confidence: float,
                                         exploration_rate: float) -> float:
        """
        计算认知熵因子，用于动态调整奖励权重

        Args:
            training_step: 当前训练步数
            model_confidence: 模型置信度 [0, 1]
            exploration_rate: 当前探索率 [0, 1]

        Returns:
            认知熵调整因子
        """
        # 基于训练进度的熵衰减
        max_steps = 100000  # 假设最大训练步数
        time_factor = min(training_step / max_steps, 1.0)

        # 认知熵计算（基于模型置信度和探索率）
        cognitive_entropy = -model_confidence * math.log(model_confidence + 1e-8) - \
                           (1 - model_confidence) * math.log(1 - model_confidence + 1e-8)

        # 动态调整因子
        entropy_factor = 1.0 + cognitive_entropy * (1.0 - time_factor) * exploration_rate

        return entropy_factor

    def calculate_total_reward(self,
                             state: Dict,
                             action: Dict,
                             next_state: Dict,
                             done: bool,
                             info: Dict) -> float:
        """
        计算总奖励

        Args:
            state: 当前状态
            action: 执行的动作
            next_state: 下一状态
            done: 是否结束
            info: 额外信息

        Returns:
            总奖励值
        """
        total_reward = 0.0

        # 提取状态信息
        own_pos = np.array(state['own_position'])
        own_vel = np.array(state['own_velocity'])
        own_heading = state['own_heading']
        target_pos = np.array(state['target_position'])
        target_ships = state.get('target_ships', [])

        next_own_pos = np.array(next_state['own_position'])
        current_speed = np.linalg.norm(own_vel)

        # 检查是否到达目标
        distance_to_target = np.linalg.norm(target_pos - next_own_pos)
        if distance_to_target < 50.0:  # 到达目标
            total_reward += self.goal_reward
            return total_reward

        # 检查碰撞
        collision_risks = []
        min_distance = float('inf')

        for target_ship in target_ships:
            target_ship_pos = np.array(target_ship['position'])
            target_ship_vel = np.array(target_ship['velocity'])

            # 计算DCPA和TCPA
            dcpa, tcpa = self.calculate_dcpa_tcpa(own_pos, own_vel,
                                                target_ship_pos, target_ship_vel)

            # 计算碰撞风险
            collision_risk = self.calculate_collision_risk(dcpa, tcpa)
            collision_risks.append(collision_risk)

            # 更新最小距离
            current_distance = np.linalg.norm(target_ship_pos - next_own_pos)
            min_distance = min(min_distance, current_distance)

            # 检查实际碰撞
            if current_distance < self.ship_length:
                total_reward += self.collision_penalty
                return total_reward

        # 计算各项奖励
        # 1. 安全性奖励
        safety_reward = self.calculate_safety_reward(collision_risks, min_distance)
        total_reward += safety_reward

        # 2. 效率性奖励
        efficiency_reward = self.calculate_efficiency_reward(
            next_own_pos, target_pos, own_pos, current_speed)
        total_reward += efficiency_reward

        # 3. COLREGs合规性奖励
        if target_ships:
            # 选择风险最高的目标船进行COLREGs评估
            max_risk_idx = np.argmax(collision_risks)
            if collision_risks[max_risk_idx] > 0.1:
                target_ship = target_ships[max_risk_idx]
                target_bearing = self.calculate_bearing(own_pos,
                                                      np.array(target_ship['position']))
                encounter_type = self.get_encounter_type(own_heading, target_bearing)
                colregs_reward = self.calculate_colregs_compliance(
                    encounter_type, action, collision_risks[max_risk_idx])
                total_reward += colregs_reward

        # 4. 步数惩罚
        total_reward += self.step_penalty

        # 5. 认知熵动态调整
        if 'training_step' in info and 'model_confidence' in info:
            entropy_factor = self.calculate_cognitive_entropy_factor(
                info['training_step'],
                info['model_confidence'],
                info.get('exploration_rate', 0.1))
            total_reward *= entropy_factor

        return total_reward

    def calculate_bearing(self, from_pos: np.ndarray, to_pos: np.ndarray) -> float:
        """
        计算从一个位置到另一个位置的方位角

        Args:
            from_pos: 起始位置 [x, y]
            to_pos: 目标位置 [x, y]

        Returns:
            方位角 (度) [0, 360)
        """
        delta = to_pos - from_pos
        bearing = math.atan2(delta[1], delta[0]) * 180.0 / math.pi
        return (bearing + 360.0) % 360.0

    def get_reward_info(self) -> Dict:
        """
        获取奖励函数的详细信息

        Returns:
            奖励函数配置信息
        """
        return {
            'ship_length': self.ship_length,
            'safety_radius': self.safety_radius,
            'target_speed': self.target_speed,
            'weights': {
                'colregs': self.colregs_weight,
                'safety': self.safety_weight,
                'efficiency': self.efficiency_weight
            },
            'penalties_rewards': {
                'collision_penalty': self.collision_penalty,
                'goal_reward': self.goal_reward,
                'step_penalty': self.step_penalty
            }
        }


# 使用示例
if __name__ == "__main__":
    # 创建奖励函数实例
    reward_function = ShipCollisionAvoidanceReward(
        ship_length=100.0,
        safety_radius_multiplier=1.5,
        target_speed=10.0
    )

    # 示例状态
    state = {
        'own_position': [0.0, 0.0],
        'own_velocity': [8.0, 0.0],
        'own_heading': 0.0,
        'target_position': [1000.0, 0.0],
        'target_ships': [
            {
                'position': [500.0, 100.0],
                'velocity': [0.0, -5.0]
            }
        ]
    }

    # 示例动作
    action = {
        'rudder_angle': 10.0,  # 向右转10度
        'thrust': 0.8
    }

    # 下一状态
    next_state = {
        'own_position': [8.0, 0.0],
        'own_velocity': [8.0, 0.0],
        'own_heading': 2.0,
        'target_position': [1000.0, 0.0],
        'target_ships': [
            {
                'position': [500.0, 95.0],
                'velocity': [0.0, -5.0]
            }
        ]
    }

    # 计算奖励
    info = {
        'training_step': 1000,
        'model_confidence': 0.7,
        'exploration_rate': 0.2
    }

    reward = reward_function.calculate_total_reward(
        state, action, next_state, False, info)

    print(f"计算得到的奖励值: {reward:.4f}")
    print("奖励函数配置信息:")
    print(reward_function.get_reward_info())
