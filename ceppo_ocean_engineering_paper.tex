%% 
%% Copyright 2019-2024 Elsevier Ltd
%% 
%% Version 2.4
%% 
%% This file is part of the 'CAS Bundle'.
%% --------------------------------------
%% 
%% Template article for cas-dc documentclass for 
%% double column output.

%\documentclass[a4paper,fleqn,longmktitle]{cas-dc}
\documentclass[a4paper,fleqn]{cas-dc}

%\usepackage[authoryear,longnamesfirst]{natbib}
%\usepackage[authoryear]{natbib}
\usepackage[numbers]{natbib}

%%%Author definitions
\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
\tsc{EP}
\tsc{PMS}
\tsc{BEC}
\tsc{DE}
%%%

\begin{document}
\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}
\shorttitle{CEPPO Algorithm for Autonomous Ship Collision Avoidance}
\shortauthors{First Author et~al.}

\title [mode = title]{New Cognitive Entropy Proximal Policy Optimization (CEPPO) Algorithm for Autonomous Ship Collision Avoidance based on Deep Reinforcement Learning}                      
\tnotemark[1,2]

\tnotetext[1]{This research was supported by [Grant Information].}

\tnotetext[2]{Corresponding author information and additional funding details.}


\author[1,3]{First Author}[type=editor,
                        auid=000,bioid=1,
                        prefix=Dr,
                        role=Researcher,
                        orcid=0000-0001-0000-0000]
\cormark[1]
\fnmark[1]
\ead{<EMAIL>}
\ead[url]{www.institution.edu/first-author}

\credit{Conceptualization of this study, Methodology, Software}

\affiliation[1]{organization={Department of Naval Architecture and Ocean Engineering},
                addressline={University Address}, 
                city={City},
                postcode={Postcode}, 
                state={State},
                country={Country}}

\author[2,4]{Second Author}[style=chinese]

\author[2,3]{Third Author}[%
   role=Co-ordinator,
   suffix=Jr,
   ]
\fnmark[2]
\ead{<EMAIL>}
\ead[URL]{https://www.institution.edu}

\credit{Data curation, Writing - Original draft preparation}

\affiliation[2]{organization={Department of Marine Engineering},
                addressline={University Address}, 
                postcode={Postcode}, 
                postcodesep={}, 
                city={City},
                country={Country}}

\author[1,3]{Fourth Author}
\cormark[2]
\fnmark[1,3]
\ead{<EMAIL>}
\ead[URL]{www.institution.edu}

\affiliation[3]{organization={Department of Intelligent Systems},
                addressline={University Address}, 
                city={City},
                postcode={Postcode}, 
                state={State}, 
                country={Country}}

\cortext[cor1]{Corresponding author}
\cortext[cor2]{Principal corresponding author}
\fntext[fn1]{Present address: Department of Naval Architecture and Ocean Engineering}
\fntext[fn2]{Present address: Department of Marine Engineering}

\nonumnote{This work demonstrates the formation of a new cognitive entropy-based optimization algorithm for autonomous ship collision avoidance in complex maritime environments.}

\begin{abstract}
Due to the complexity of maritime environments and the high-dimensional nature of ship collision avoidance decisions, existing Deep Reinforcement Learning (DRL) algorithms exhibit significant deficiencies in balancing the exploration-exploitation trade-off in policy learning. Insufficient exploration in the early training stages causes the agent to prematurely converge to suboptimal solutions, thereby directly affecting the convergence rate and decision-making performance of the intelligent agent. In response to the aforementioned challenge, this research proposes a Cognitive Entropy-based Proximal Policy Optimization algorithm (CEPPO). This algorithm optimizes the training process through a three-stage dynamic adjustment mechanism: in the early training phase, it enhances exploration capability to effectively avoid local optima, while introducing entropy regularization and reward normalization mechanisms to reduce policy gradient variance; in the mid-training phase, it adopts adaptive balancing of exploration and exploitation to accelerate algorithm convergence; in the late training phase, it gradually decreases exploration to ensure policy stability and generalization capabilities. Additionally, this paper utilizes the Unreal Engine to construct a simulation platform for simulating complex dynamic scenarios. It also designs a multi-layered reward mechanism to further optimize collision avoidance strategies.
\end{abstract}

\begin{graphicalabstract}
\includegraphics{figs/ceppo-graphical-abstract.pdf}
\end{graphicalabstract}

\begin{highlights}
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment, achieving adaptive control of unmanned boat collision avoidance strategy exploration
\item A new Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies according to their cognitive state
\item A high-fidelity simulation platform based on Unreal Engine 5 (UE5) is constructed, comprehensively verifying the robustness and adaptability of the proposed algorithm in complex marine environments
\item The algorithm demonstrates superior performance in multi-ship collision avoidance scenarios compared to existing DRL methods
\end{highlights}

\begin{keywords}
Autonomous ship collision avoidance \sep Cognitive entropy \sep Deep reinforcement learning \sep Proximal Policy Optimization (PPO) \sep Maritime safety \sep Unmanned surface vehicle
\end{keywords}


\maketitle

\section{Introduction}

With the rapid development of the maritime economy and intelligent navigation, unmanned ship technology is increasingly becoming the focus of the shipping industry. Unmanned ships have advantages such as all-weather operation, remote control, and intelligent autonomous decision-making, which can effectively reduce labor costs and enhance maritime safety and efficiency. However, unmanned ships still face numerous technical challenges during actual operations, with collision avoidance being particularly prominent. The problem of collision avoidance in unmanned ships encompasses not only the safety of navigational operations but also the compliance with maritime traffic regulations and the ability to make intelligent decisions in complex settings. This represents a critical challenge that urgently demands resolution within the advanced domain of autonomous maritime transport.

Currently, there have been numerous research achievements in the field of autonomous collision avoidance for unmanned ships. Traditional collision avoidance algorithms such as the A* algorithm, artificial potential field method, and genetic algorithms have been widely applied. Langbein (2010) \cite{ref1} and Li (2017) \cite{ref2} each proposed path planning methods based on an improved A* algorithm, which enhanced the smoothness of the paths. However, they still faced issues such as excessive turning points and redundant collision assessments. In response, Sun et al. (2023) significantly enhanced the practical applicability of their algorithm by incorporating ant colony optimization (ACO) with adaptive step size and bidirectional cooperation strategies \cite{ref3}. However, this approach still falls short in autonomous decision-making and dynamic collision avoidance in unmanned boats.

In terms of autonomous collision avoidance for unmanned ships, Goodwin et al. (1975) first introduced the domain model for ships by statistically analyzing the behavior of vessels in open waters \cite{ref4}. This model divides the area around a ship into multiple virtual safety zones, triggering collision avoidance maneuvers when obstacles enter these zones. It uses CPA (Closest Point of Approach), DCPA (Distance at Closest Point of Approach), and TCPA (Time to Closest Point of Approach) to assess the spatial and temporal dimensions of collision risk. Building on this foundation, Mou et al. (2010) developed a dynamic risk assessment method based on linear regression using AIS data \cite{ref5}, which incorporates correlations between CPA and factors such as ship size, speed, and course, though still not achieving a quantitative evaluation of the collision risk index. Zhen et al. (2017) quantified the degree of collision risk through cluster analysis based on DSCBN clustering analysis and AIS data \cite{ref6}. However, with the increasing complexity of modern maritime systems, many challenges arise in forming comprehensive collision avoidance models \cite{ref7}, leading to significant uncertainties in the practical application of model-based algorithms.

With the development of artificial intelligence, intelligent algorithms represented by reinforcement learning have gained widespread attention in the field of ship collision avoidance. Model-free reinforcement learning, due to its simple structure and suitability for complex systems \cite{ref8}, has been widely applied in autonomous navigation. Yang et al. (2014) proposed a path planning method for unmanned ships based on Q-learning \cite{ref9}, which comprehensively considers ship motion models and maneuvering characteristics, obtaining the optimal strategy through learning the action-state model. However, traditional reinforcement learning algorithms typically require the construction and maintenance of a state-value function table, which is inefficient in high-dimensional action-state spaces and can even lead to state-space explosion issues. Additionally, due to the limited perceptual capabilities of reinforcement learning algorithms towards the environment, it is challenging to fully explore all possible action-state information. To address this, researchers have introduced deep neural networks to improve reinforcement learning algorithms, resulting in Deep Reinforcement Learning (DRL) \cite{ref10}. DRL combines the perceptual abilities of Deep Learning (DL) with the decision-making capabilities of Reinforcement Learning (RL) \cite{ref11}. Deep learning provides a learning objective for reinforcement learning, while reinforcement learning furnishes a learning mechanism for deep learning, thereby making deep reinforcement learning more adaptable to complex control strategies.

Currently, numerous researchers have explored the application of Deep Reinforcement Learning (DRL) in path planning and have gradually applied it to autonomous collision avoidance for ships \cite{ref12}. Zhao and colleagues proposed a path planning method using the Deep Deterministic Policy Gradient (DDPG) algorithm \cite{ref13}, utilizing AIS data to train the DRL model, which exhibits good convergence speed and stability. However, the absence of a ship motion model in the analysis weakens the stability when addressing practical issues. Zhou and his team designed a semi-Markov decision model and neural network architecture based on the DQN algorithm for the USV collision avoidance problem \cite{ref14}. Experimental results indicate this method effectively solves multi-ship collision avoidance issues, but the use of visual image data as input for DQN results in high computational demands, slowing convergence speeds. Yuan and colleagues introduced a path generation method based on DRL, combining ship domains and CPA for collision risk assessment and producing collision avoidance paths in compliance with COLREGs \cite{ref15}. Recently, a predictive-decision joint collision avoidance algorithm based on DDPG was introduced \cite{ref16}. It employs a dual-layer state-space design, integrating the Velocity Obstacle (VO) model to predict potential collision zones and optimizing training efficiency and collision avoidance strategies through a dense reward mechanism. The algorithm's performance in typical scenarios and complex multi-ship environments has been validated on the Unity3D simulation platform and in real tests, demonstrating excellent safety, stability, and practical value.

However, due to the complexity of the maritime collision avoidance environment, the existing methods still have significant shortcomings in balancing exploration and exploitation in collision avoidance strategies, leading to either insufficient exploration or slow convergence, thus affecting the global optimization capabilities. To address this issue, Lai et al. \cite{ref17} introduced an entropy regularization strategy to enhance exploratory capabilities, but due to the fixed entropy coefficient settings, there are still problems with insufficient exploration at the beginning of training or oscillating convergence later on. Chen et al. proposed a Dynamically Adjusted Entropy Proximal Policy Optimization (DAE-PPO) algorithm \cite{ref18}, which uses a quadratic decrement entropy method to optimize the exploration mechanism, thereby further improving the performance of exploration and exploitation in the strategy.

This paper introduces an innovative Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm, in which the change in entropy depends not only on training time but also on the model's cognition for nonlinear adjustments. This approach achieves a dynamic balance between exploration and exploitation, significantly enhancing the training efficiency, convergence speed, and global optimization capabilities of the collision avoidance strategy. Specifically, at the initial stage of training, unmanned boats have an imperfect understanding of the environment, and CEPPO adopts a higher entropy value to guide the agent in extensive exploration, avoiding falling into local optima. As the training progresses and the agent's cognitive abilities gradually improve, CEPPO dynamically adjusts the entropy value according to the model's learning progress, achieving an optimal balance between exploration and exploitation and accelerating the convergence speed. In the later stages of training, as the agent's understanding of the environment stabilizes, the entropy value is further reduced to enhance the stability and global optimality of the strategy.

This innovation allows the algorithm to adaptively perceive its own learning state and adjust its exploration strategy, thereby achieving more efficient and robust collision avoidance decision optimization in complex dynamic environments.

The main contributions of this study include:
\begin{enumerate}
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment, achieving adaptive control of unmanned boat collision avoidance strategy exploration.
\item A new Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies according to their cognitive state during the learning process of collision avoidance strategies. This optimization balances exploration and exploitation, achieving efficient and stable collision avoidance decision learning in complex maritime environments.
\item A high-fidelity simulation platform based on Unreal Engine 5 (UE5) is constructed, comprehensively verifying the robustness and adaptability of the proposed algorithm in complex marine environments through simulation of dynamic multi-ship intersecting scenarios.
\end{enumerate}

The rest of this paper is organized as follows: Section 2 reviews related research; Section 3 introduces the basic framework of the ship collision avoidance system; Section 4 provides a detailed description of the CEPPO algorithm and the designed reward functions; experimental design and simulation results are presented in Section 5; finally, Section 6 summarizes the research contributions and discusses future work directions.

\section{Mathematical Model of Ship Motion and Collision Risk Assessment}\label{sec:mathematical_model}

This section presents the six degrees of freedom ship motion model of Unmanned Surface Vehicles (USVs) and the collision risk assessment method. The model takes into account multiple factors such as ship motion attitude, propeller thrust, and rudder angle control to simulate the motion behavior of USVs during navigation.

\subsection{Six Degrees of Freedom Ship Motion Dynamics Mathematical Model}\label{subsec:ship_motion_model}

The formulated USV kinematic model can accurately describe its motion characteristics in virtual environments using mathematical language, providing a theoretical foundation for the construction and implementation of autonomous collision avoidance algorithms. The system can be expressed as six coupled differential equations:

\begin{equation}
\begin{cases}
m(\dot{u} - rv + qw) = X_{thrust} + X_{drag} + X_{wave} + X_{current} \\
m(\dot{v} + ru - pw) = Y_{thrust} + Y_{drag} + Y_{wave} + Y_{current} \\
m(\dot{w} - qu + pv) = Z_{buoyancy} + Z_{drag} + Z_{wave} \\
I_x \dot{p} + (I_z - I_y)qr = K_{thrust} + K_{wave} \\
I_y \dot{q} + (I_x - I_z)pr = M_{wave} \\
I_z \dot{r} + (I_y - I_x)pq = N_{thrust} + N_{drag} + N_{wave} + N_{current}
\end{cases}
\label{eq:ship_motion}
\end{equation}

Where $I_x$, $I_y$, and $I_z$ represent the moments of inertia of the ship around the three principal axes, and $m$ is the ship's mass. The variables $u$, $v$, and $w$ represent the linear velocities along the surge, sway, and heave directions, respectively, while $p$, $q$, and $r$ represent the angular velocities around the roll, pitch, and yaw axes.

\subsection{Risk Assessment}\label{subsec:risk_assessment}

The collision risk assessment is based on DCPA (Distance to Closest Point of Approach) and TCPA (Time to Closest Point of Approach). The collision risk index is calculated as:

\begin{equation}
CR = \exp\left[-\frac{DCPA}{c_1}\right] - \exp\left[-\frac{TCPA}{c_2}\right]
\label{eq:collision_risk}
\end{equation}

where $c_1$ and $c_2$ are scaling parameters. The DCPA and TCPA are calculated as:

\begin{equation}
DCPA = D \sin(\varphi_r - \alpha_{Tn} - \pi)
\label{eq:dcpa}
\end{equation}

\begin{equation}
TCPA = \frac{D \cos(\varphi_r - \alpha_{Tn} - \pi)}{v_r}
\label{eq:tcpa}
\end{equation}

where $D$ is the current distance between ships, $\varphi_r$ is the relative bearing, $\alpha_{Tn}$ is the target ship's course angle, and $v_r$ is the relative velocity.

\section{Cognitive Entropy-based Proximal Policy Optimization}\label{sec:ceppo}

This section introduces the CEPPO algorithm, including its improvement principles, the design of its state and action spaces, and the construction of its reward function.

\subsection{The Proximal Policy Optimization (PPO) Algorithm}\label{subsec:ppo_algorithm}

The process of ship collision avoidance can be defined as a Markov Decision Process (MDP). The optimal policy $\pi^*$ is obtained by maximizing the expected cumulative reward:

\begin{equation}
\pi^* = \arg \max_{\pi_\theta} E_{\tau \sim \pi_\theta} \left[ \sum_{t=0}^{\infty} \gamma^t R(s_t, a_t) \right]
\label{eq:optimal_policy}
\end{equation}

where $\gamma$ is the discount factor, $R(s_t, a_t)$ is the reward function, and $\tau$ represents a trajectory.

PPO constrains the policy update step by introducing a clipped surrogate objective function:

\begin{equation}
L^{PPO}(\theta) = \hat{E}_t \left[ \min \left( r_t(\theta) \hat{A}_t, \text{clip}(r_t(\theta), 1-\varepsilon, 1+\varepsilon) \hat{A}_t \right) \right]
\label{eq:ppo_loss}
\end{equation}

where $r_t(\theta) = \frac{\pi_\theta(a_t|s_t)}{\pi_{\theta_{old}}(a_t|s_t)}$ is the probability ratio, $\hat{A}_t$ is the advantage estimate, and $\varepsilon$ is the clipping parameter.

\subsection{Reinforcement Learning Optimization Based on Dynamic Cognitive Entropy}\label{subsec:rl_optimization}

To enhance the capability of policy exploration, an entropy regularization term is introduced. The entropy of a policy is defined as:

\begin{equation}
H(\pi(a_t | s_t)) = -\sum \pi(a_t | s_t) \log \pi(a_t | s_t)
\label{eq:entropy}
\end{equation}

The cognitive entropy coefficient is dynamically adjusted based on the agent's learning progress:

\begin{equation}
\lambda_{entropy}(t) = \lambda_{max} \cdot \exp\left(-\alpha \cdot \frac{t}{T_{total}} \cdot C(t)\right)
\label{eq:cognitive_entropy}
\end{equation}

where $C(t)$ represents the cognitive state of the agent at time $t$, $\alpha$ is the decay rate, and $T_{total}$ is the total training time.

The total loss function combines policy optimization, value function estimation, and exploration incentives:

\begin{equation}
L^{Total}(\theta, \omega) = \hat{E}_t \left[ L^{PPO}(\theta) - \lambda_1 L^{VF}(\omega) + \lambda_{entropy}(t) H(\pi(a_t | s_t)) \right]
\label{eq:total_loss}
\end{equation}

where $L^{VF}(\omega)$ is the value function loss and $\lambda_1$ is the value function coefficient.

\section{Experimental Results and Analysis}\label{sec:experimental_results}

This section presents the experimental setup, results, and comprehensive analysis of the proposed CEPPO algorithm. The experiments were conducted using a high-fidelity simulation platform based on Unreal Engine 5, which provides realistic maritime environments for testing collision avoidance algorithms.

\subsection{Experimental Setup}\label{subsec:experimental_setup}

The simulation environment includes various scenarios with multiple ships, different weather conditions, and complex maritime traffic patterns. The performance of CEPPO is compared against baseline algorithms including PPO, DDPG, and DAE-PPO.

\subsection{Performance Evaluation}\label{subsec:performance_evaluation}

The experimental results demonstrate that CEPPO achieves superior performance in terms of convergence speed, collision avoidance success rate, and path optimality compared to existing methods.

\section{Conclusion}\label{sec:conclusion}

This paper proposed a novel CEPPO algorithm for autonomous ship collision avoidance, demonstrating superior performance in complex maritime environments through comprehensive experimental validation. The cognitive entropy mechanism enables adaptive exploration control, leading to more efficient and robust collision avoidance strategies.

\section*{Declaration of competing interests}
The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

\section*{Data availability}
Data will be made available on request.

\section*{Acknowledgments}
This research was supported by [Grant Information]. The authors would like to thank the reviewers for their valuable comments and suggestions.

\appendix
\section{Algorithm Implementation Details}
Appendix sections are coded under \verb+\appendix+.

\verb+\printcredits+ command is used after appendix sections to list
author credit taxonomy contribution roles tagged using \verb+\credit+
in frontmatter.

\printcredits

%% Loading bibliography style file
%\bibliographystyle{model1-num-names}
\bibliographystyle{cas-model2-names}

% Loading bibliography database
\bibliography{cas-refs}


%\vskip3pt

\bio{}
Author biography without author photo.
First Author received the Ph.D. degree in Naval Architecture and Ocean Engineering from [University Name] in [Year]. His research interests include autonomous ship navigation, deep reinforcement learning, and maritime safety systems. He has published over [X] papers in international journals and conferences.
\endbio

\bio{figs/author-photo1}
Second Author biography with author photo.
Second Author received the M.S. degree in Marine Engineering from [University Name] in [Year]. Her research focuses on intelligent control systems for unmanned surface vehicles and collision avoidance algorithms. She is currently pursuing her Ph.D. degree in the same field.
\endbio

\bio{figs/author-photo2}
Third Author biography with author photo.
Third Author is a Professor in the Department of Marine Engineering at [University Name]. He has over [X] years of experience in maritime engineering and has supervised numerous graduate students in the field of autonomous navigation systems.
\endbio

\end{document}
