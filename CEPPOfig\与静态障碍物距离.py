import pandas as pd
import matplotlib.pyplot as plt

# ===== 文件路径与算法标签 =====
file_paths = [
    "C:/Users/<USER>/Desktop/实验数据（终）/静态障碍物/CEPPO.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/静态障碍物/线性熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/静态障碍物/固定熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/静态障碍物/PPO.xlsx"
]

algorithm_labels = [
    "CEPPO",
    "Linear Entropy PPO",
    "Fixed Entropy PPO",
    "Classic PPO"
]

# ===== 每种算法对应颜色（统一3条障碍线颜色）=====
colors = ["#e76f51", "#e8c56a", "#299d91", "#8bb17b"]

# ===== 绘图 =====
plt.figure(figsize=(10, 6))

# ===== 遍历每个文件绘图 =====
for file_path, algo_label, color in zip(file_paths, algorithm_labels, colors):
    df = pd.read_excel(file_path)
    distances_cm = df.iloc[:, 6:9]
    distances_m = distances_cm / 100.0
    distances_m = distances_m.clip(upper=200)

    time_steps = df.index * 0.3  # 每帧0.3秒，转换为时间轴（秒）

    for i in range(3):  # 三个障碍物
        plt.plot(time_steps, distances_m.iloc[:, i], label=f"{algo_label} - Obstacle {i+1}",
                 color=color)

# ===== 图表修饰 =====
plt.xlabel("Time (s)")
plt.ylabel("Distance to Obstacle (m)")
plt.title("Real-time Distance to Obstacles (Four Algorithms)")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
