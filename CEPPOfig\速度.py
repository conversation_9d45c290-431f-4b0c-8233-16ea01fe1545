import pandas as pd
import matplotlib.pyplot as plt

# 读取 Excel 文件
file_path = "C:/Users/<USER>/Desktop/实验数据/无障碍物/CEPPO.xlsx"  # 修改为你的文件路径
df = pd.read_excel(file_path)

# 提取第3列和第4列（注意：pandas是从0开始计数，所以是df.iloc[:, 2] 和 df.iloc[:, 3]）
x_speed = df.iloc[:, 2]
y_speed = df.iloc[:, 3]

# 创建图形
plt.figure(figsize=(10, 6))

# 绘制x轴速度曲线
plt.plot(x_speed, label="X-axis Speed", linestyle='-', linewidth=2)

# 绘制y轴速度曲线
plt.plot(y_speed, label="Y-axis Speed", linestyle='--', linewidth=2)

# 添加图例、标题、坐标轴标签
plt.legend()
plt.title("X and Y Axis Speeds Over Time")
plt.xlabel("Time Step")
plt.ylabel("Speed (m/s)")
plt.grid(True)

# 显示图形
plt.tight_layout()
plt.show()
