%% 
%% Copyright 2019-2024 Elsevier Ltd
%% 
%% This file is part of the 'CAS Bundle'.
%% --------------------------------------
%% 
%% Template article for cas-dc documentclass for 
%% double column output.
%% Ocean Engineering Journal Paper

\documentclass[a4paper,fleqn]{cas-dc}

% If the frontmatter runs over more than one page
% use the longmktitle option.
%\documentclass[a4paper,fleqn,longmktitle]{cas-dc}

%\usepackage[numbers]{natbib}
%\usepackage[authoryear]{natbib}
\usepackage[authoryear,longnamesfirst]{natbib}

%%%Author macros
\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
%%%

% Uncomment and use as if needed
%\newtheorem{theorem}{Theorem}
%\newtheorem{lemma}[theorem]{Lemma}
%\newdefinition{rmk}{Remark}
%\newproof{pf}{Proof}
%\newproof{pot}{Proof of Theorem \ref{thm}}

\begin{document}
\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}

% Short title
\shorttitle{CEPPO Algorithm for Autonomous Ship Collision Avoidance}    

% Short author
\shortauthors{First Author et al.}  

% Main title of the paper
\title [mode = title]{New Cognitive Entropy Proximal Policy Optimization (CEPPO) Algorithm for Autonomous Ship Collision Avoidance based on Deep Reinforcement Learning}  

% Title footnote mark
\tnotemark[1] 

% Title footnote 1.
\tnotetext[1]{This research was supported by [Grant Information]} 

% First author
\author[1]{First Author}

% Corresponding author indication
\cormark[1]

% Footnote of the first author
\fnmark[1]

% Email id of the first author
\ead{<EMAIL>}

% URL of the first author
\ead[url]{https://www.institution.edu/first-author}

% Credit authorship
\credit{Conceptualization of this study, Methodology, Software, Writing - Original Draft}

% Address/affiliation
\affiliation[1]{organization={Department of Naval Architecture and Ocean Engineering},
            addressline={University Address}, 
            city={City},
            postcode={Postcode}, 
            state={State},
            country={Country}}

\author[2]{Second Author}

% Footnote of the second author
\fnmark[2]

% Email id of the second author
\ead{<EMAIL>}

% URL of the second author
\ead[url]{https://www.institution.edu/second-author}

% Credit authorship
\credit{Validation, Formal Analysis, Writing - Review \& Editing}

% Address/affiliation
\affiliation[2]{organization={Department of Marine Engineering},
            addressline={University Address}, 
            city={City},
            postcode={Postcode}, 
            state={State},
            country={Country}}

% Corresponding author text
\cortext[1]{Corresponding author}

% Footnote text
\fntext[1]{Present address: Department of Naval Architecture and Ocean Engineering}
\fntext[2]{Present address: Department of Marine Engineering}

% Here goes the abstract
\begin{abstract}
Due to the complexity of maritime environments and the high-dimensional nature of ship collision avoidance decisions, existing Deep Reinforcement Learning (DRL) algorithms exhibit significant deficiencies in balancing the exploration-exploitation trade-off in policy learning. Insufficient exploration in the early training stages causes the agent to prematurely converge to suboptimal solutions, thereby directly affecting the convergence rate and decision-making performance of the intelligent agent. In response to the aforementioned challenge, this research proposes a Cognitive Entropy-based Proximal Policy Optimization algorithm (CEPPO). This algorithm optimizes the training process through a three-stage dynamic adjustment mechanism: in the early training phase, it enhances exploration capability to effectively avoid local optima, while introducing entropy regularization and reward normalization mechanisms to reduce policy gradient variance; in the mid-training phase, it adopts adaptive balancing of exploration and exploitation to accelerate algorithm convergence; in the late training phase, it gradually decreases exploration to ensure policy stability and generalization capabilities. Additionally, this paper utilizes the Unreal Engine to construct a simulation platform for simulating complex dynamic scenarios. It also designs a multi-layered reward mechanism to further optimize collision avoidance strategies.
\end{abstract}

% Research highlights
\begin{highlights}
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment, achieving adaptive control of unmanned boat collision avoidance strategy exploration
\item A new Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies according to their cognitive state
\item A high-fidelity simulation platform based on Unreal Engine 5 (UE5) is constructed, comprehensively verifying the robustness and adaptability of the proposed algorithm in complex marine environments
\item The algorithm demonstrates superior performance in multi-ship collision avoidance scenarios compared to existing DRL methods
\end{highlights}

% Keywords
% Each keyword is seperated by \sep
\begin{keywords}
Autonomous ship collision avoidance \sep Cognitive entropy \sep Deep reinforcement learning \sep Proximal Policy Optimization (PPO) \sep Maritime safety \sep Unmanned surface vehicle
\end{keywords}

\maketitle

% Main text
\section{Introduction}\label{sec:introduction}

With the rapid development of the maritime economy and intelligent navigation, unmanned ship technology is increasingly becoming the focus of the shipping industry. Unmanned ships have advantages such as all-weather operation, remote control, and intelligent autonomous decision-making, which can effectively reduce labor costs and enhance maritime safety and efficiency. However, unmanned ships still face numerous technical challenges during actual operations, with collision avoidance being particularly prominent. The problem of collision avoidance in unmanned ships encompasses not only the safety of navigational operations but also the compliance with maritime traffic regulations and the ability to make intelligent decisions in complex settings. This represents a critical challenge that urgently demands resolution within the advanced domain of autonomous maritime transport.

Currently, there have been numerous research achievements in the field of autonomous collision avoidance for unmanned ships. Traditional collision avoidance algorithms such as the A* algorithm, artificial potential field method, and genetic algorithms have been widely applied. Langbein (2010) and Li (2017) each proposed path planning methods based on an improved A* algorithm, which enhanced the smoothness of the paths \citep{ref1,ref2}. However, they still faced issues such as excessive turning points and redundant collision assessments. In response, Sun et al. (2023) significantly enhanced the practical applicability of their algorithm by incorporating ant colony optimization (ACO) with adaptive step size and bidirectional cooperation strategies \citep{ref3}. However, this approach still falls short in autonomous decision-making and dynamic collision avoidance in unmanned boats.

In terms of autonomous collision avoidance for unmanned ships, \citet{ref4} first introduced the domain model for ships by statistically analyzing the behavior of vessels in open waters. This model divides the area around a ship into multiple virtual safety zones, triggering collision avoidance maneuvers when obstacles enter these zones. It uses CPA (Closest Point of Approach), DCPA (Distance at Closest Point of Approach), and TCPA (Time to Closest Point of Approach) to assess the spatial and temporal dimensions of collision risk. Building on this foundation, \citet{ref5} developed a dynamic risk assessment method based on linear regression using AIS data, which incorporates correlations between CPA and factors such as ship size, speed, and course, though still not achieving a quantitative evaluation of the collision risk index. \citet{ref6} quantified the degree of collision risk through cluster analysis based on DSCBN clustering analysis and AIS data. However, with the increasing complexity of modern maritime systems, many challenges arise in forming comprehensive collision avoidance models \citep{ref7}, leading to significant uncertainties in the practical application of model-based algorithms.

With the development of artificial intelligence, intelligent algorithms represented by reinforcement learning have gained widespread attention in the field of ship collision avoidance. Model-free reinforcement learning, due to its simple structure and suitability for complex systems \citep{ref8}, has been widely applied in autonomous navigation. \citet{ref9} proposed a path planning method for unmanned ships based on Q-learning, which comprehensively considers ship motion models and maneuvering characteristics, obtaining the optimal strategy through learning the action-state model. However, traditional reinforcement learning algorithms typically require the construction and maintenance of a state-value function table, which is inefficient in high-dimensional action-state spaces and can even lead to state-space explosion issues. Additionally, due to the limited perceptual capabilities of reinforcement learning algorithms towards the environment, it is challenging to fully explore all possible action-state information. To address this, researchers have introduced deep neural networks to improve reinforcement learning algorithms, resulting in Deep Reinforcement Learning (DRL) \citep{ref10}. DRL combines the perceptual abilities of Deep Learning (DL) with the decision-making capabilities of Reinforcement Learning (RL) \citep{ref11}. Deep learning provides a learning objective for reinforcement learning, while reinforcement learning furnishes a learning mechanism for deep learning, thereby making deep reinforcement learning more adaptable to complex control strategies.

Currently, numerous researchers have explored the application of Deep Reinforcement Learning (DRL) in path planning and have gradually applied it to autonomous collision avoidance for ships \citep{ref12}. \citet{ref13} proposed a path planning method using the Deep Deterministic Policy Gradient (DDPG) algorithm, utilizing AIS data to train the DRL model, which exhibits good convergence speed and stability. However, the absence of a ship motion model in the analysis weakens the stability when addressing practical issues. \citet{ref14} designed a semi-Markov decision model and neural network architecture based on the DQN algorithm for the USV collision avoidance problem. Experimental results indicate this method effectively solves multi-ship collision avoidance issues, but the use of visual image data as input for DQN results in high computational demands, slowing convergence speeds. \citet{ref15} introduced a path generation method based on DRL, combining ship domains and CPA for collision risk assessment and producing collision avoidance paths in compliance with COLREGs. Recently, a predictive-decision joint collision avoidance algorithm based on DDPG was introduced \citep{ref16}. It employs a dual-layer state-space design, integrating the Velocity Obstacle (VO) model to predict potential collision zones and optimizing training efficiency and collision avoidance strategies through a dense reward mechanism. The algorithm's performance in typical scenarios and complex multi-ship environments has been validated on the Unity3D simulation platform and in real tests, demonstrating excellent safety, stability, and practical value.

However, due to the complexity of the maritime collision avoidance environment, the existing methods still have significant shortcomings in balancing exploration and exploitation in collision avoidance strategies, leading to either insufficient exploration or slow convergence, thus affecting the global optimization capabilities. To address this issue, \citet{ref17} introduced an entropy regularization strategy to enhance exploratory capabilities, but due to the fixed entropy coefficient settings, there are still problems with insufficient exploration at the beginning of training or oscillating convergence later on. \citet{ref18} proposed a Dynamically Adjusted Entropy Proximal Policy Optimization (DAE-PPO) algorithm, which uses a quadratic decrement entropy method to optimize the exploration mechanism, thereby further improving the performance of exploration and exploitation in the strategy.

This paper introduces an innovative Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm, in which the change in entropy depends not only on training time but also on the model's cognition for nonlinear adjustments. This approach achieves a dynamic balance between exploration and exploitation, significantly enhancing the training efficiency, convergence speed, and global optimization capabilities of the collision avoidance strategy.

The main contributions of this study include:
\begin{enumerate}
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment, achieving adaptive control of unmanned boat collision avoidance strategy exploration.
\item A new Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies according to their cognitive state during the learning process of collision avoidance strategies.
\item A high-fidelity simulation platform based on Unreal Engine 5 (UE5) is constructed, comprehensively verifying the robustness and adaptability of the proposed algorithm in complex marine environments through simulation of dynamic multi-ship intersecting scenarios.
\end{enumerate}

The rest of this paper is organized as follows: Section 2 reviews related research; Section 3 introduces the basic framework of the ship collision avoidance system; Section 4 provides a detailed description of the CEPPO algorithm and the designed reward functions; experimental design and simulation results are presented in Section 5; finally, Section 6 summarizes the research contributions and discusses future work directions.

\section{Mathematical Model of Ship Motion and Collision Risk Assessment}\label{sec:mathematical_model}

This section presents the six degrees of freedom ship motion model of Unmanned Surface Vehicles (USVs) and the collision risk assessment method. The model takes into account multiple factors such as ship motion attitude, propeller thrust, and rudder angle control to simulate the motion behavior of USVs during navigation.

\subsection{Six Degrees of Freedom Ship Motion Dynamics Mathematical Model}\label{subsec:ship_motion_model}

The formulated USV kinematic model can accurately describe its motion characteristics in virtual environments using mathematical language, providing a theoretical foundation for the construction and implementation of autonomous collision avoidance algorithms. This study considers the disturbance forces and moments caused by water flow to more accurately describe the dynamic characteristics of USVs in complex marine environments.

\section{Cognitive Entropy-based Proximal Policy Optimization}\label{sec:ceppo}

This section introduces the CEPPO algorithm, including its improvement principles, the design of its state and action spaces, and the construction of its reward function.

\section{Experimental Results and Analysis}\label{sec:experimental_results}

This section presents the experimental setup, results, and comprehensive analysis of the proposed CEPPO algorithm.

\section{Conclusion}\label{sec:conclusion}

This paper proposed a novel CEPPO algorithm for autonomous ship collision avoidance, demonstrating superior performance in complex maritime environments through comprehensive experimental validation.

% To print the credit authorship contribution details
\printcredits

%% Loading bibliography style file
\bibliographystyle{cas-model2-names}

% Loading bibliography database
\bibliography{cas-refs}

\end{document}
