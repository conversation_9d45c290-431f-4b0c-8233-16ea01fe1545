\documentclass[final,3p,twocolumn]{elsarticle}

\usepackage{lineno,hyperref}
\modulolinenumbers[5]

\journal{Ocean Engineering}

%%%%%%%%%%%%%%%%%%%%%%%
%% Elsevier bibliography styles
%%%%%%%%%%%%%%%%%%%%%%%
%% To change the style, put a % in front of the second line of the current style and
%% remove the % from the second line of the style you would like to use.
%%%%%%%%%%%%%%%%%%%%%%%

%% Numbered
%\bibliographystyle{model1-num-names}

%% Numbered without titles
%\bibliographystyle{model1a-num-names}

%% Harvard
%\bibliographystyle{model2-names.bst}\biboptions{authoryear}

%% Vancouver numbered
%\bibliographystyle{model3-num-names}

%% Vancouver name/year
%\bibliographystyle{model4-names}\biboptions{authoryear}

%% APA style
%\bibliographystyle{model5-names}\biboptions{authoryear}

%% AMA style
%\bibliographystyle{model6-num-names}

%% `Elsevier LaTeX' style
\bibliographystyle{elsarticle-num}
%%%%%%%%%%%%%%%%%%%%%%%

\begin{document}

\begin{frontmatter}

\title{New Cognitive Entropy Proximal Policy Optimization (CEPPO) Algorithm for Autonomous Ship Collision Avoidance based on Deep Reinforcement Learning}

%% Group authors per affiliation:
\author[mymainaddress]{First Author\corref{mycorrespondingauthor}}
\cortext[mycorrespondingauthor]{Corresponding author}
\ead{<EMAIL>}

\author[mysecondaryaddress]{Second Author}
\ead{<EMAIL>}

\address[mymainaddress]{Department of Naval Architecture and Ocean Engineering, University Name, City, Country}
\address[mysecondaryaddress]{Department of Marine Engineering, University Name, City, Country}

\begin{abstract}
Due to the complexity of maritime environments and the high-dimensional nature of ship collision avoidance decisions, existing Deep Reinforcement Learning (DRL) algorithms exhibit significant deficiencies in balancing the exploration-exploitation trade-off in policy learning. Insufficient exploration in the early training stages causes the agent to prematurely converge to suboptimal solutions, thereby directly affecting the convergence rate and decision-making performance of the intelligent agent. In response to the aforementioned challenge, this research proposes a Cognitive Entropy-based Proximal Policy Optimization algorithm (CEPPO). This algorithm optimizes the training process through a three-stage dynamic adjustment mechanism: in the early training phase, it enhances exploration capability to effectively avoid local optima, while introducing entropy regularization and reward normalization mechanisms to reduce policy gradient variance; in the mid-training phase, it adopts adaptive balancing of exploration and exploitation to accelerate algorithm convergence; in the late training phase, it gradually decreases exploration to ensure policy stability and generalization capabilities. Additionally, this paper utilizes the Unreal Engine to construct a simulation platform for simulating complex dynamic scenarios. It also designs a multi-layered reward mechanism to further optimize collision avoidance strategies.
\end{abstract}

\begin{keyword}
Autonomous ship collision avoidance \sep Cognitive entropy \sep Deep reinforcement learning \sep Proximal Policy Optimization (PPO)
\end{keyword}

\end{frontmatter}

\linenumbers

\section{Introduction}
\label{sec:introduction}

With the rapid development of the maritime economy and intelligent navigation, unmanned ship technology is increasingly becoming the focus of the shipping industry. Unmanned ships have advantages such as all-weather operation, remote control, and intelligent autonomous decision-making, which can effectively reduce labor costs and enhance maritime safety and efficiency. However, unmanned ships still face numerous technical challenges during actual operations, with collision avoidance being particularly prominent. The problem of collision avoidance in unmanned ships encompasses not only the safety of navigational operations but also the compliance with maritime traffic regulations and the ability to make intelligent decisions in complex settings. This represents a critical challenge that urgently demands resolution within the advanced domain of autonomous maritime transport.

Currently, there have been numerous research achievements in the field of autonomous collision avoidance for unmanned ships. Traditional collision avoidance algorithms such as the A* algorithm, artificial potential field method, and genetic algorithms have been widely applied. Langbein (2010) and Li (2017) respectively proposed path planning methods based on improved A* algorithms \cite{langbein2010,li2017}, improving path smoothness, but still facing problems such as excessive turning points and redundant collision assessment. To address this, Sun et al. (2023) based on the Ant Colony Optimization (ACO) algorithm \cite{sun2023}, combined adaptive step size with bidirectional collaborative strategies, significantly improving the practical applicability of the algorithm. However, such algorithms are relatively insufficient in autonomous decision-making and dynamic collision avoidance for unmanned boats.

For the risk assessment aspect of autonomous collision avoidance in unmanned ships, Goodwin et al. (1975) first proposed the ship domain model through statistical analysis of ship behavior in open waters \cite{goodwin1975}. This model divides the area around ships into multiple virtual safety zones, triggering collision avoidance actions when obstacles enter these zones, and uses CPA (Closest Point of Approach), DCPA (Distance to Closest Point of Approach), and TCPA (Time to Closest Point of Approach) to assess the spatial and temporal dimensions of collision risk. Based on this, Mou et al. (2010) established a dynamic risk assessment method based on linear regression using AIS data \cite{mou2010}, incorporating the correlation between CPA and ship dimensions, speed, heading, etc., but still failed to achieve quantitative assessment of collision risk indices. Zhen et al. (2017) quantified collision danger through clustering analysis based on DSCBN clustering analysis and AIS data \cite{zhen2017}. However, with the increasing complexity of modern maritime systems, many problems are difficult to establish complete collision avoidance models \cite{ref7}, leading to significant uncertainties in model-based algorithms during practical applications.

With the development of artificial intelligence, intelligent algorithms represented by reinforcement learning have received widespread attention in the field of ship collision avoidance. Model-free reinforcement learning, due to its simple structure and adaptability to complex systems \cite{ref8}, has been widely applied in autonomous navigation. Yang et al. (2014) proposed a Q-learning-based unmanned ship path planning method \cite{yang2014}, comprehensively considering ship motion models and maneuvering characteristics, obtaining optimal strategies through learning action-state models. However, traditional reinforcement learning algorithms usually need to construct and maintain state value function tables, which are inefficient in high-dimensional action-state spaces and may even cause state space explosion problems. Additionally, due to the limited environmental perception capabilities of reinforcement learning algorithms, it is difficult to fully explore all possible action-state information. To address this, researchers introduced deep neural networks to improve reinforcement learning algorithms \cite{ref10}, forming Deep Reinforcement Learning (DRL) algorithms. Deep reinforcement learning algorithms combine the perception capabilities of deep learning (DL) and the decision-making capabilities of reinforcement learning (RL) \cite{ref11}. Deep learning provides learning objectives for reinforcement learning, while reinforcement learning provides learning mechanisms for deep learning, making deep reinforcement learning more adaptable to complex control strategies.

Currently, many researchers have explored the application of deep reinforcement learning in path planning and gradually applied it to autonomous collision avoidance of ships \cite{ref12}. Zhao et al. proposed a path planning method based on the Deep Deterministic Policy Gradient (DDPG) algorithm \cite{zhao2019}, using AIS data to train the DRL model, which has good convergence speed and stability, but due to not considering the ship motion model, its stability is not strong when solving practical problems. Zhou et al. designed a semi-Markov decision model and neural network architecture for USV collision avoidance problems based on the DQN algorithm \cite{zhou2020}, and experimental results showed that this method can effectively solve multi-ship collision avoidance problems, but because visual image information was used as input to DQN in the study, the excessive computational load led to slow convergence speed. Yuan et al. proposed a DRL-based path generation method, combining ship domain and CPA for collision risk assessment, and generating appropriate collision avoidance paths based on COLREGs-compliant collision avoidance decisions \cite{yuan2021}. Recently, researchers proposed a prediction-decision joint collision avoidance algorithm based on DDPG \cite{ref16}, adopting a dual-layer state space design, combining the Velocity Obstacle (VO) model to predict potential collision areas, and optimizing training efficiency and collision avoidance strategies through dense reward mechanisms. The algorithm verified its excellent performance in typical scenarios and complex multi-ship environments through Unity3D simulation platform and actual testing, showing good safety, stability, and application value.

However, due to the complexity of maritime collision avoidance environments, the above methods still have significant deficiencies in research on the balance between exploration and exploitation of collision avoidance strategies, leading to insufficient exploration or slow convergence, thus affecting global optimization capabilities. To address this problem, Lai et al. \cite{lai2022} introduced entropy regularization strategies to enhance exploration capabilities, but due to their fixed entropy coefficient settings, there are still problems of insufficient exploration in the early training stage or oscillatory convergence in the later stage. Chen et al. proposed a Dynamic Adjustment Entropy Proximal Policy Optimization (DAE-PPO) algorithm \cite{chen2023}, adopting a quadratic decreasing entropy method to optimize the exploration mechanism, further improving the exploration and exploitation performance of strategies.

This paper introduces an innovative Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm, in which the change in entropy depends not only on training time but also on the model's cognition for nonlinear adjustments. This approach achieves a dynamic balance between exploration and exploitation, significantly enhancing the training efficiency, convergence speed, and global optimization capabilities of the collision avoidance strategy. Specifically, at the initial stage of training, unmanned boats have an imperfect understanding of the environment, and CEPPO adopts a higher entropy value to guide the agent in extensive exploration, avoiding falling into local optima. As the training progresses and the agent's cognitive abilities gradually improve, CEPPO dynamically adjusts the entropy value according to the model's learning progress, achieving an optimal balance between exploration and exploitation and accelerating the convergence speed. In the later stages of training, as the agent's understanding of the environment stabilizes, the entropy value is further reduced to enhance the stability and global optimality of the strategy.

This innovation allows the algorithm to adaptively perceive its own learning state and adjust its exploration strategy, thereby achieving more efficient and robust collision avoidance decision optimization in complex dynamic environments.

The main contributions of this study include:
\begin{enumerate}
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment, achieving adaptive control of unmanned boat collision avoidance strategy exploration.
\item A new Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies according to their cognitive state during the learning process of collision avoidance strategies. This optimization balances exploration and exploitation, achieving efficient and stable collision avoidance decision learning in complex maritime environments.
\item A high-fidelity simulation platform based on Unreal Engine 5 (UE5) is constructed, comprehensively verifying the robustness and adaptability of the proposed algorithm in complex marine environments through simulation of dynamic multi-ship intersecting scenarios.
\end{enumerate}

The rest of this paper is organized as follows: Section 2 reviews related research; Section 3 introduces the basic framework of the ship collision avoidance system; Section 4 provides a detailed description of the CEPPO algorithm and the designed reward functions; experimental design and simulation results are presented in Section 5; finally, Section 6 summarizes the research contributions and discusses future work directions.

\section{Mathematical Model of Ship Motion and Collision Risk Assessment Model}
\label{sec:mathematical_model}

This chapter presents the six degrees of freedom ship motion model of Unmanned Surface Vehicles (USVs) and the collision risk assessment method. The model takes into account multiple factors such as ship motion attitude, propeller thrust, and rudder angle control to simulate the motion behavior of USVs during navigation. Additionally, based on the COLREGs, typical encounter situations and collision avoidance strategies are categorized, and in conjunction with the maritime domain, a collision risk coefficient model is constructed. This model quantifies collision risks, providing theoretical support for collision decision-making in unmanned vessels.

\subsection{Six Degrees of Freedom Ship Motion Dynamics Mathematical Model}
\label{subsec:ship_motion_model}

The formulated USV kinematic model can accurately describe its motion characteristics in virtual environments using mathematical language, providing a theoretical foundation for the construction and implementation of autonomous collision avoidance algorithms. This study considers the disturbance forces and moments caused by water flow to more accurately describe the dynamic characteristics of USVs in complex marine environments. To achieve precise simulation of USV navigation states, this paper adopts a modular modeling approach, constructing a six-degree-of-freedom dynamic system based on the MMG (Modular Mathematical Group) model\cite{ref21}, comprehensively describing the linear and rotational motion of USVs in three-dimensional space. Under the body-fixed coordinate system, this system can be expressed as six coupled differential equations, corresponding to the linear acceleration of the hull's center of mass and angular acceleration around each axis, controlled by the combined action of multiple external forces including thrust, hydrodynamic forces, wave disturbances, current disturbances, and buoyancy, as shown in Equation (1)\cite{ref20}:

\begin{equation}
\begin{cases}
m(\dot{u} - rv + qw) = X_{thrust} + X_{drag} + X_{wave} + X_{current} \\
m(\dot{v} + ru - pw) = Y_{thrust} + Y_{drag} + Y_{wave} + Y_{current} \\
m(\dot{w} - qu + pv) = Z_{buoyancy} + Z_{drag} + Z_{wave} \\
I_x \dot{p} + (I_z - I_y)qr = K_{thrust} + K_{wave} \\
I_y \dot{q} + (I_x - I_z)pr = M_{wave} \\
I_z \dot{r} + (I_y - I_x)pq = N_{thrust} + N_{drag} + N_{wave} + N_{current}
\end{cases}
\label{eq:ship_motion}
\end{equation}

Where $I_x$, $I_y$, and $I_z$ represent the moments of inertia of the ship around the three principal axes (longitudinal, transverse, and vertical axes) in the body coordinate system, respectively, and $m$ is the ship's mass. The state variables $(u,v,w)$ correspond to the longitudinal velocity (surge), lateral velocity (sway), and vertical velocity (heave) of the hull in the body coordinate system, while $(p,q,r)$ represent the roll rate, pitch rate, and yaw rate of the ship, respectively.

The thrust components and their associated moments can be represented by the vector group $(X_{thrust}, Y_{thrust}, K_{thrust}, N_{thrust})$, which respectively represent the longitudinal force, lateral force, and control moments in the roll and yaw directions generated under the influence of propeller thrust $F_{thrust}$ and heading angle $\beta$\cite{ref21}. Here, $d$ and $h$ are the lateral and vertical offset distances of the thrust application point relative to the hull's center of mass, as shown in Equation (2):

\begin{equation}
\begin{cases}
X_{thrust} = F_{thrust} \cdot \cos(\beta) \\
Y_{thrust} = F_{thrust} \cdot \sin(\beta) \\
N_{thrust} = Y_{thrust} \cdot d = F_{thrust} \cdot \sin(\beta) \cdot d \\
K_{thrust} = Y_{thrust} \cdot h = F_{thrust} \cdot \sin(\beta) \cdot h
\end{cases}
\label{eq:thrust_forces}
\end{equation}

During navigation, the hull is affected by drag and moments generated by water viscosity, which are mainly represented by the vector terms $(X_{drag}, Y_{drag}, Z_{drag}, N_{drag})$. These components reflect the fluid viscous drag and rotational damping acting on the hull surface, where parameters $C_d$ and $C_{yaw\_drag}$ are the linear drag coefficient and fluid damping coefficient in the yaw direction, respectively.

Furthermore, current disturbances are introduced as external disturbance forces in the dynamic model, contained in $(X_{current}, Y_{current}, N_{current})$, which are determined by the current velocities $u_{current}$ and $v_{current}$ in the $x$ and $y$ directions of the body coordinate system, as well as the rotational velocity $r_{current}$. The corresponding current drag coefficient is $C_{current}$ and the rotational disturbance coefficient is $C_{current\_yaw}$, as shown in Equations (3) and (4)\cite{ref30}:

\begin{equation}
\begin{cases}
X_{drag} = -C_d \cdot u \cdot |u| \\
Y_{drag} = -C_d \cdot v \cdot |v| \\
Z_{drag} = -C_d \cdot w \cdot |w| \\
N_{drag} = -C_{yaw\_drag} \cdot r \cdot |r|
\end{cases}
\label{eq:drag_forces}
\end{equation}

\begin{equation}
\begin{cases}
X_{current} = -C_{current} \cdot (u - u_{current}) \cdot |u - u_{current}| \\
Y_{current} = -C_{current} \cdot (v - v_{current}) \cdot |v - v_{current}| \\
N_{current} = -C_{current\_yaw} \cdot (r - r_{current})
\end{cases}
\label{eq:current_forces}
\end{equation}

另一方面，USV 在水体中所受的浮力 $Z_{buoyancy}$ 由排水体积决定。具体而言，其大小取决于当前船体浸入水体的体积 $V_{sub}$，并满足阿基米德原理，如式（5）所示\cite{ref31}：

\begin{equation}
Z_{buoyancy} = -\rho g V_{sub}
\label{eq:buoyancy}
\end{equation}

波浪对船舶所施加的干扰力和力矩可通过向量组 $(X_{wave}, Y_{wave}, Z_{wave}, K_{wave}, M_{wave}, N_{wave})$ 进行统一表述。这些波浪激励项是由多个方向、多个周期的波浪叠加形成的合成扰动力，广泛存在于真实海洋环境中。其中，$P$ 表示波浪在某一时刻对船体浸水表面施加的瞬时压力分布，其作用通过体积积分方式转化为各方向上的合力与合力矩，如式(6)(7)所示\cite{ref32}：

\begin{equation}
\begin{cases}
X_{wave} = -\iiint_{V_{sub}} \frac{\partial P}{\partial x} dV_{sub} \\
Y_{wave} = -\iiint_{V_{sub}} \frac{\partial P}{\partial y} dV_{sub} \\
Z_{wave} = -\iiint_{V_{sub}} \frac{\partial P}{\partial z} dV_{sub}
\end{cases}
\label{eq:wave_forces}
\end{equation}

\begin{equation}
\begin{cases}
K_{wave} = \iiint_{V_{sub}} \left(\frac{\partial P}{\partial z} z - \frac{\partial P}{\partial z} v\right) dV_{sub} \\
M_{wave} = \iiint_{V_{sub}} \left(\frac{\partial P}{\partial z} x - \frac{\partial P}{\partial x} z\right) dV_{sub} \\
N_{wave} = \iiint_{V_{sub}} \left(\frac{\partial P}{\partial x} y - \frac{\partial P}{\partial y} x\right) dV_{sub}
\end{cases}
\label{eq:wave_moments}
\end{equation}

基于该动力学建模框架，系统能够在仿真环境中实时输出船舶的完整状态信息，包括空间位置、速度、姿态等，并依据当前控制策略（如舵角、推力）预测下一时刻的状态变化。这些高维、动态状态数据可直接输入作为深度强化学习（DRL）智能体的状态空间组成部分，参与策略决策过程，从而帮助学习算法完成对控制行为的自适应优化与时序反馈控制。

\subsection{Risk Assessment}
\label{subsec:risk_assessment}

\subsubsection{COLREGS}
\label{subsubsec:colregs}

The International Regulations for Preventing Collisions at Sea (COLREGs) are a set of mandatory rules established by the International Maritime Organization (IMO) in 1972, designed to regulate the behavior of vessels underway and prevent maritime collision incidents. These rules specify in detail the responsibilities and obligations of vessels in various encounter scenarios and provide specific guidance for collision avoidance maneuvers. As internationally recognized maritime regulations, the COLREGs are closely related to international maritime law and serve as an essential legal basis for ensuring the safety of navigation at sea. Therefore, in the collision avoidance decision systems of Maritime Autonomous Surface Ships (MASSs), it is essential to strictly adhere to the COLREGs in designing operational strategies to ensure safe navigation and comply with international maritime regulations.

Within the COLREGs, detailed rules have been formulated for common situations involving ship collision avoidance, primarily covering the following typical encounter scenarios (refer to Figure 2):

\begin{enumerate}
\item \textbf{Head on situation}

When the own ship (OS) and target ship (TS) have a relative bearing within the [000°, 005°] range and there is a risk of collision, both vessels should turn to starboard (right) in accordance with Rule 14 of the COLREGs.

\item \textbf{Crossing (give way)}

When the target ship is located on the starboard (right) side of the own ship and the relative bearing is within the [005°, 112.5°] range with a risk of collision, the own ship is the give-way vessel and should turn to starboard (right) as mandated by Rule 15 of the COLREGs.

\item \textbf{Crossing (stand on)}

When the target ship is located on the port (left) side of the own ship and the relative bearing is within the [247.5°, 355°] range with a risk of collision, the target ship is the give-way vessel. If it fails to take appropriate action to avoid a collision, the own ship must still take evasive action as stipulated by Rule 17(a) of the COLREGs.

\item \textbf{Overtaking}

When the target ship is located behind the own ship, with a relative bearing between [112.5°, 247.5°] and there is a risk of collision, the overtaking vessel should pass on the starboard side while the vessel being overtaken should maintain its course, as specified in Rule 13 of the COLREGs.
\end{enumerate}

% Figure 2: COLREGS encounter scenarios
\begin{figure}[htbp]
\centering
% \includegraphics[width=0.8\textwidth]{figures/colregs_scenarios.png}
\fbox{\parbox{0.8\textwidth}{\centering
\vspace{2cm}
Figure 2: COLREGS Encounter Scenarios\\
(Head-on, Crossing Give-way, Crossing Stand-on, Overtaking)\\
\vspace{2cm}
}}
\caption{Typical encounter scenarios defined by COLREGs showing relative bearing ranges for different collision avoidance situations.}
\label{fig:colregs_scenarios}
\end{figure}

\subsubsection{碰撞风险评估}
\label{subsubsec:collision_risk_assessment}

船舶领域作为航行安全中的核心概念，最早由日本学者Fujii和Tanaka（1971）提出，其本质是通过在船舶周围划定动态的安全警戒区域，以预防碰撞风险并量化船舶间的安全边界\cite{fujii1971}。在开阔水域中，船舶的航行自由度较高，航向具有显著的多向性和不确定性。相较于更适合直线型航道环境的椭圆形船舶领域（Goodwin, 1975）\cite{goodwin1975}，圆形船舶领域因其各向同性的空间防护特性，能够更有效应对开阔水域中船舶可能从任意方向接近的复杂会遇情况（Coldwell, 1983）\cite{coldwell1983}。此外，圆形领域模型在计算效率和实用性方面具有优势，能够快速评估多船会遇情况下的碰撞风险（Wang et al., 2009）\cite{wang2009}。因此，本研究基于圆形船舶领域，采用1.5倍船长作为防护半径，构建360°等距安全圈，并结合谨慎航行角度模型，有效提升开阔水域中多船避碰决策的空间约束能力（Pan et al., 2025）\cite{pan2025}。

碰撞风险（Collision Risk, CR）是船舶航行安全评估中的核心量化指标，其通过解析船舶间的动态空间关系，预测潜在碰撞可能性并指导避碰决策。本研究基于船舶领域模型构建CR评估体系，旨在将复杂的航行态势转化为可量化的碰撞风险指标，从而简化避碰决策逻辑。具体而言，CR的计算依托最近接近点距离（Distance to Closest Point of Approach, DCPA）与到达最近接近点时间（Time to Closest Point of Approach, TCPA）的协同分析：DCPA表征两船最小安全间距的临界阈值，TCPA反映碰撞威胁的紧迫性，二者通过空间几何与运动学模型实现动态耦合，共同构成CR的量化基础。该模型不仅能够实时识别航行态势的潜在威胁，还可通过风险值的梯度变化为智能体提供清晰的避碰方向，显著增强决策过程的可解释性与控制精度。

为确定最近接近点距离（$DCPA$）与到达最近接近点时间（$TCPA$）的耦合系数，本研究构建了基于船舶运动特性的参数化模型。模型综合考虑本船（Own Ship, OS）与目标船（Target Ship, TS）的船型尺度、相对航速以及避碰操纵响应距离等核心要素。研究设定当TS进入OS的态势感知边界距离（$d_r$）时，碰撞风险（$CR$）需收敛至预设的安全阈值（Allowable Collision Risk, $CR_{al}$）。其中$d_r$为OS通过AIS与雷达系统持续监测TS的起始距离，其值由船舶制动性能与海况条件联合标定；$CR_{al}$作为避碰决策触发条件，通过蒙特卡洛仿真模拟多船会遇场景，逆向推演出兼顾安全性与经济性的最优阈值。

% Figure 3: DCPA and TCPA calculation diagram
\begin{figure}[htbp]
\centering
% \includegraphics[width=0.8\textwidth]{figures/dcpa_tcpa_diagram.png}
\fbox{\parbox{0.8\textwidth}{\centering
\vspace{2cm}
图3 DCPA和TCPA求解示意图\\
(Own Ship, Target Ship, Relative Motion Parameters)\\
\vspace{2cm}
}}
\caption{DCPA和TCPA求解示意图，显示两船相对运动参数和几何关系。}
\label{fig:dcpa_tcpa_diagram}
\end{figure}

如图3所示：$D$表示两船间的距离，$\varphi_r$表示目标船相对于本船的相对运动航向，$\alpha_{Tn}$表示目标船相对本船的方位角，$v_0$、$v_1$、$v_r$分别表示本船、目标船的航速以及目标船相对于本船的相对速度。这些参数构成了船舶避碰的综合风险评估体系，为船舶风险评估提供了一种直观可靠的方法。因此，本文设计的$CR$评估公式如下\cite{ref27}：

\begin{equation}
CR = \exp\left[-\frac{DCPA}{c_1}\right] - \exp\left[-\frac{TCPA}{c_2}\right]
\label{eq:collision_risk}
\end{equation}

两船间到达最近接近点距离（$DCPA$）的计算公式如下：

\begin{equation}
DCPA = D \sin(\varphi_r - \alpha_{Tn} - \pi)
\label{eq:dcpa}
\end{equation}

两船间到达最近接近点时间（$TCPA$）的计算公式如下：

\begin{equation}
TCPA = \frac{D \cos(\varphi_r - \alpha_{Tn} - \pi))}{v_r}
\label{eq:tcpa}
\end{equation}

\section{Cognitive Entropy-based Proximal Policy Optimization}
\label{sec:ceppo}

This section introduces CEPPO, including its improvement principles, the design of its state and action spaces, and the construction of its reward function. The algorithm can flexibly control exploratory behaviors based on the progress of learning from the environment, achieving adaptive regulation of collision avoidance strategy exploration for unmanned boats. Furthermore, by designing multidimensional state spaces and continuous action spaces, the algorithm comprehensively describes the navigational state of the ship and dynamic environmental information. Combined with a refined reward function system, it effectively enhances the ship's adaptability and decision-making precision in complex environments. CEPPO demonstrates exceptional performance in dynamic obstacle detection and path planning, optimizing the efficiency and safety of the algorithm and showcasing its advantages in autonomous navigation applications.

\subsection{The Proximal Policy Optimization (PPO) algorithm}
\label{subsec:ppo_algorithm}

The process of ship collision avoidance can be defined as a sequential decision-making problem, typically described using a Markov Decision Process (MDP)\cite{ref28}. It is defined as a quintuple $MDP =< S, A, P, R, \gamma >$ Here, $S$ and $A$ represent the state space and action space, respectively. $P(s'|s,a)$ refers to the state transition function, which represents the conditional probability distribution of moving from the current state ($s$) to the next state($s'$) after taking action($a$). This transition is influenced by a combination of the dynamics model, environmental disturbances, and collision avoidance strategies. The reward function($R(s,a)$) integrates considerations of collision risk, path planning efficiency, and regulatory compliance, aiming to motivate the vessel to achieve safe and efficient collision avoidance decisions. $\gamma$ is the discount factor, used to balance immediate rewards with long-term gains. The strategy $\pi(a|o)$ for each vessel is a mapping from its partially observable state to the action space, representing the decision rule for selecting the optimal collision avoidance action based on current observational information. The global joint strategy describes the collaborative behavior of all vessels in a dynamic environment, aimed at achieving the overall objective of collision avoidance.

In reinforcement learning, the optimal policy $\pi^*$ is obtained by maximizing the expected cumulative reward. Specifically, the optimal policy $\pi^*$ satisfies the following condition:

\begin{equation}
\pi^* = \arg \max_{\pi_\theta} E_{\tau \sim \pi_\theta} \left[ \sum_{t=0}^{\infty} \gamma^t R(s_t, a_t) \right]
\label{eq:optimal_policy}
\end{equation}

In this context, $\tau$ represents the trajectory, $\gamma$ indicates the discount factor, and $R(s_t, a_t)$ is the reward function at time t. To efficiently solve for the optimal policy, this study employs the Proximal Policy Optimization (PPO) algorithm, which is based on the Actor-Critic network framework. By collaboratively optimizing the policy function and the value function, this approach facilitates efficient and stable reinforcement learning training.

The Actor network takes the environmental state $s_t$ as input and outputs the action probability distribution $\pi_\theta(a_t|s_t)$, where $\theta$ represents the policy parameters. Additionally, the Actor network generates action decisions by maximizing cumulative rewards and uses a clipping mechanism to constrain the magnitude of policy updates, thus preventing training divergence. The mathematical formulation is as follows:

\begin{equation}
a_t \sim \pi_\theta(\cdot | s_t)
\label{eq:action_sampling}
\end{equation}

The Critic network takes the state $s_t$ as input and outputs the estimated state value $V_\omega(s_t)$, where $\omega$ represents the value function parameters, and $r_{t+k}$ is the immediate reward at the k-th future step. Its objective is to minimize the mean squared error between the predicted values and the actual returns:

\begin{equation}
L^{VF}(\omega) = \frac{1}{N} \sum_{i=1}^{N} [V_\omega(s_i) - R_i]^2
\label{eq:value_function_loss}
\end{equation}

The actual returns, $R_i$, are calculated using the Generalized Advantage Estimation (GAE) combined with the state value, specifically:

\begin{equation}
R_i = \hat{A}_i + V_\omega(s_i)
\label{eq:actual_returns}
\end{equation}

PPO constrains the policy update step by introducing a clipped surrogate objective function, whose expression is:

\begin{equation}
L^{PPO}(\theta) = \hat{E}_t \left[ \min \left( r_t(\theta) \hat{A}_t, \text{clip}(r_t(\theta), 1-\varepsilon, 1+\varepsilon) \hat{A}_t \right) \right]
\label{eq:ppo_loss}
\end{equation}

In this context, $r_t(\theta) = \frac{\pi_{\theta_{new}}(a_t|s_t)}{\pi_{\theta_{old}}(a_t|s_t)}$ represents the ratio of the probabilities between the new and old policies. The clipping function, $\text{clip}(r_t(\theta), 1-\varepsilon, 1+\varepsilon)$, constrains the ratio $r_t(\theta)$ within a specified range $[1-\varepsilon, 1+\varepsilon]$. $\hat{A}_t$, the Generalized Advantage Estimation (GAE), is defined as follows:

\begin{equation}
\hat{A}_t = \sum_{l=0}^{T-t} (\gamma\lambda)^l \delta_{t+l}
\label{eq:gae}
\end{equation}

\begin{equation}
\delta_t = r_t + \gamma V(s_{t+1}) - V(s_t)
\label{eq:td_error}
\end{equation}

In this formula, $\gamma$ represents the discount factor, $\lambda$ are the parameters of GAE, and $V(s)$ is the state value function.

% Figure 4: PPO Algorithm Flowchart
\begin{figure}[htbp]
\centering
% \includegraphics[width=0.8\textwidth]{figures/ppo_flowchart.png}
\fbox{\parbox{0.8\textwidth}{\centering
\vspace{2cm}
Figure 5: Flowchart of the PPO Algorithm\\
(Actor-Critic Network, Policy Update, Value Function)\\
\vspace{2cm}
}}
\caption{PPO算法流程图，展示Actor-Critic网络结构和策略更新过程。}
\label{fig:ppo_flowchart}
\end{figure}

The schematic diagram of the PPO algorithm is shown in Figure 5.

\subsection{Reinforcement Learning Optimization Based on Dynamic Cognitive Entropy}
\label{subsec:rl_optimization}

To enhance the capability of policy exploration, an entropy regularization term is introduced\cite{ref29}, as shown in equation (18). Here, $H(\pi(a_t|s_t))$ denotes the entropy of the policy distribution for a given state $s_t$. Entropy is used to measure the randomness or uncertainty in the action distribution of a policy. By incorporating entropy into the loss function, the algorithm encourages the policy to become less deterministic, thereby promoting exploration. This term prevents premature convergence to local optima by maximizing the entropy of the policy. The larger this value, the more uniform the action distribution of the policy, and the stronger its exploratory behavior.

\begin{equation}
H(\pi(a_t | s_t)) = -\sum \pi(a_t | s_t) \log \pi(a_t | s_t)
\label{eq:entropy}
\end{equation}

Combining policy optimization (Actor), value function estimation (Critic), and exploration incentives, the total loss function of the PPO algorithm is presented as in equation (19):

\begin{equation}
L^{Total}(\theta, \omega) = \hat{E}_t \left[ L^{PPO}(\theta) - \lambda_1 L^{VF}(\omega) + \lambda_2 H(\pi(a_t | s_t)) \right]
\label{eq:total_loss}
\end{equation}

In the formula, $\lambda_1$ denotes the coefficient of the value function, which controls the weight of the estimated loss $L^{VF}$ for the value function, influencing the accuracy of the Critic network's state value predictions. Entropy coefficient $\lambda_2$ has a value range from 0 to 1, used to adaptively adjust the intensity of exploration during the training process. When $\lambda_2 = 0$, the policy is completely deterministic (no exploration), and the optimization is dominated by the Critic network, which may easily lead to premature convergence to local optima. When $\lambda_2 = 1$, the policy is highly random (strong exploration), with the Actor network prioritized for exploring new actions, but this may reduce training efficiency or cause oscillations.

Although entropy regularization plays an important role in enhancing policy exploration, a fixed entropy coefficient cannot flexibly balance exploration and exploitation during training. Particularly in the early stages of training, a fixed $\lambda_2$ may not provide sufficient exploration, limiting the discovery of globally optimal strategies; in the later stages, it may cause the policy to lose balance due to random disturbances during the convergence phase.

\subsection{Algorithm Implementation}
\label{subsec:algorithm_implementation}

% Algorithm implementation details

\subsection{State and Action Space Design}
\label{subsec:state_action_space}

% State and action space design

\subsection{Reward Function}
\label{subsec:reward_function}

% Reward function design

\section{Experimental Results and Analysis}
\label{sec:experimental_results}

\subsection{Training Environment}
\label{subsec:training_environment}

% Training environment description

\subsection{Based on Unreal Engine, the analysis of algorithm hyperparameters and performance}
\label{subsec:unreal_engine_analysis}

% Unreal Engine based analysis

\subsection{Experimental Results}
\label{subsec:experimental_results}

% Experimental results

\subsection{Discussion}
\label{subsec:discussion}

% Discussion content

\section{结论与展望}
\label{sec:conclusion}

% Conclusion and future work content

\section*{Acknowledgments}
% Acknowledgments

\section*{Declaration of competing interests}
The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

\section*{Data availability}
Data will be made available on request.

%% The Appendices part is started with the command \appendix;
%% appendix sections are then done as normal sections
%% \appendix

%% \section{}
%% \label{}

%% If you have bibdatabase file and want bibtex to generate the
%% biboliography, use the following:
%%\bibliography{<your-bib-database>}

%% else use the following coding to input the biboliography directly:
\begin{thebibliography}{99}

\bibitem{ref1}
Erckens, H., Büsser, G.-A., Pradalier, C., \& Siegwart, R.Y. (2010). Navigation strategy and trajectory following controller for an autonomous sailing vessel. IEEE RAM, 17, 47–54.

\bibitem{ref2}
Ye Li, Teng Ma, Pengyun Chen, Yanqing Jiang, Rupeng Wang \& Qiang Zhang. (2017). Autonomous underwater vehicle optimal path planning method for seabed terrain matching navigation. Ocean Engineering, 107-115.

\bibitem{ref3}
Sun, P. N., \& Zhang, Z. M. (2023). Smooth Path Planning for Unmanned Surface Vehicles Based on Ant Colony Algorithm. Electronic Science \& Technology, 36(3).

\bibitem{ref4}
Goodwin, Elisabeth M. (1975). A statistical study of ship domains. The Journal of Navigation, 28(3), 328-344.

\bibitem{ref5}
Mou, Jun Min, Cees Van der Tak, and Han Ligteringen. (2010). Study on collision avoidance in busy waterways by using AIS data. Ocean Engineering, 37(5-6), 483-490.

\bibitem{ref6}
Zhen, Rong, Maria Riveiro, and Yongxing Jin. (2017). A novel analytic framework of real-time multi-vessel collision risk assessment for maritime traffic surveillance. Ocean Engineering, 145, 492-501.

\bibitem{ref7}
谢朔, 初秀民, 柳晨光, 等. (2016). 船舶智能避碰研究综述及展望. 交通信息与安全, 34(01), 1-9.

\bibitem{ref8}
Sutton, Richard S., and Andrew G. Barto. (2018). Reinforcement learning: An introduction. MIT press.

\bibitem{ref9}
Yang, Y., Pang, Y. J., Li, H. W., \& Zhang, R. B. (2014). Research on USV Local Path Planning Method Based on Reinforcement Learning in Complex Sea Conditions. Journal of Marine Science and Application, 3(3), 333-339.

\bibitem{ref10}
Zhao, Dongbin, et al. (2016). Review of deep reinforcement learning and discussions on the development of computer Go. Control Theory \& Applications, 33(6), 701-717.

\bibitem{ref11}
Silver D, Huang A, Maddison C J, et al. (2016). Mastering the game of Go with deep neural networks and tree search. Nature, 529(7587), 484-489.

\bibitem{ref12}
Xu X, Lu Y, Liu X, et al. (2020). Intelligent collision avoidance algorithms for USVs via deep reinforcement learning under COLREGs. Ocean Engineering, 217, 107704.

\bibitem{ref13}
Zhao, J., Wang, P., Li, B., \& Bai, C. (2023). A ddpg-based usv path-planning algorithm. Applied Sciences, 13(19).

\bibitem{ref14}
Zhou, Z. G., Yu, S. Y., Yu, J. B., Duan, J. W., Chen, L., \& Chen, J. L. (2023). Research on T-DQN Intelligent Obstacle Avoidance Algorithm for Unmanned Surface Vehicles. Acta Automatica Sinica, 49(8), 1645-1655.

\bibitem{ref15}
Yuan, W., \& Rui, X. (2023). Deep reinforcement learning-based controller for dynamic positioning of an unmanned surface vehicle. Computers and Electrical Engineering.

\bibitem{ref16}
Zhao, J., Wang, P., Li, B., \& Bai, C. (2023). A ddpg-based usv path-planning algorithm. Applied Sciences, 13(19).

\bibitem{ref17}
Lai, P., Liu, Y., Zhang, W., \& Xu, H. (2023). Intelligent controller for unmanned surface vehicles by deep reinforcement learning. Physics of Fluids.

\bibitem{ref18}
Chen, G., Huang, Z., Wang, W., \& Yang, S. (2024). A Novel Dynamically Adjusted Entropy Algorithm for Collision Avoidance in Autonomous Ships Based on Deep Reinforcement Learning. Journal of Marine Science and Engineering.

\bibitem{ref19}
Yingxiang T. (2020). Research on mathematical model and dynamic positioning control algorithm of six degrees of freedom maneuvering in marine ships. Journal of Intelligent \& Fuzzy Systems, 38(2), 1299-1309.

\bibitem{ref20}
黄西密. (2015). 无人艇建模及操纵运动仿真的研究. 大连海事大学.

\bibitem{ref21}
Yasukawa, Hironori, and Yasuo Yoshimura. (2015). Introduction of MMG standard method for ship maneuvering predictions. Journal of Marine Science and Technology, 20, 37-52.

\bibitem{ref22}
Fujii, Y., \& Tanaka, M. (1971). Traffic capacity. Journal of Navigation, 24(4), 543-552.

\bibitem{ref23}
Goodwin, E. M. (1975). A statistical study of ship domains. Journal of Navigation, 28(3), 328-344.

\bibitem{ref24}
Coldwell, T. G. (1983). Marine traffic behaviour in restricted waters. Journal of Navigation, 36(3), 430-444.

\bibitem{ref25}
Wang, N., Meng, X., Xu, Q., \& Wang, Z. (2009). A unified analytical framework for ship domains. Journal of Navigation, 62(4), 643-655.

\bibitem{ref26}
Pan, W., Wang, Y., Xie, X., Li, M., \& Fan, J. (2025). Ship Collision Risk Assessment Algorithm Based on the Especial Cautious Navigation Angle Model. Journal of Marine Science and Engineering, 13(1), 173.

\bibitem{ref27}
Mou, J.M., Tak, C.V.D., Ligteringen, H. (2010). Study on Collision Avoidance in Busy Waterways by Using AIS Data. Ocean Engineering, 37, 483–490.

\bibitem{ref28}
Yang, Xiaofei, et al. (2024). A human-like collision avoidance method for USVs based on deep reinforcement learning and velocity obstacle. Expert Systems with Applications, 124388.

\bibitem{ref29}
John Schulman, Filip Wolski, Prafulla Dhariwal, et al. (2017). Proximal Policy Optimization Algorithms. arXiv preprint arXiv:1707.06347.

\bibitem{ref30}
Fossen, Thor I. (2011). Handbook of marine craft hydrodynamics and motion control. John Wiley \& Sons Ltd.

\bibitem{ref31}
高双. (2007). 高速无人艇的建模与控制仿真. 哈尔滨工程大学.

\bibitem{ref32}
Hamamoto, Masami, Yoonsoo Kim, and Katsuya Uwatoko. (1991). Study on ship motions and capsizing in following seas. Journal of the Society of Naval Architects of Japan, 170, 173-182.

\bibitem{ref33}
Boudlal, A., Khafaji, A., \& Elabbadi, J. (2024). Entropy adjustment by interpolation for exploration in Proximal Policy Optimization (PPO). Engineering Applications of Artificial Intelligence, 133, 108401.

\bibitem{ref34}
Yang, S., Wang, K., Wang, W., Wu, H., Suo, Y., Chen, G., \& Xian, J. (2025). Dual-attention proximal policy optimization for efficient autonomous navigation in narrow channels using deep reinforcement learning. Ocean Engineering, 326, 120707.

\end{thebibliography}

\end{document}
