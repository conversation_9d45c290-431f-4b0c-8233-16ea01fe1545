# -*- coding: utf-8 -*-
'''
动作遮蔽完整模拟测试
在Python端模拟整个训练过程，验证动作遮蔽效果

这个测试不需要UE5，完全在Python端模拟船舶避碰场景
'''

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
from colreg_rules import COLREGRules
import time

class MockShipEnvironment:
    """模拟船舶环境"""
    
    def __init__(self):
        self.action_count = 5
        self.obs_dim = 8  # [own_x, own_y, own_heading, target_x, target_y, target_heading, distance, relative_bearing]
        self.colreg_rules = COLREGRules(self.action_count)
        
        # 动作定义
        self.actions = {
            0: -30,  # 大幅左转 (-30度)
            1: -10,  # 小幅左转 (-10度)
            2: 0,    # 保持航向 (0度)
            3: 10,   # 小幅右转 (+10度)
            4: 30    # 大幅右转 (+30度)
        }
        
        self.reset()
    
    def reset(self):
        """重置环境"""
        # 本船初始状态
        self.own_x = 0.0
        self.own_y = 0.0
        self.own_heading = 0.0  # 向北
        
        # 随机生成目标船
        self.generate_random_target()
        
        return self.get_observation()
    
    def generate_random_target(self):
        """生成随机目标船"""
        # 随机选择会遇类型
        encounter_types = [
            (5, 180),      # 对遇
            (45, 270),     # 交叉相遇(让路)
            (300, 90),     # 交叉相遇(直行)
            (180, 0),      # 追越
        ]
        
        relative_bearing, target_heading = encounter_types[np.random.randint(0, 4)]
        
        # 目标船位置
        distance = np.random.uniform(2.0, 5.0)
        angle_rad = np.radians(relative_bearing)
        
        self.target_x = self.own_x + distance * np.sin(angle_rad)
        self.target_y = self.own_y + distance * np.cos(angle_rad)
        self.target_heading = target_heading
        
        # 计算实际相对方位角
        dx = self.target_x - self.own_x
        dy = self.target_y - self.own_y
        angle_to_target = np.degrees(np.arctan2(dx, dy))
        self.relative_bearing = (angle_to_target - self.own_heading) % 360
        self.distance = np.sqrt(dx*dx + dy*dy)
    
    def get_observation(self):
        """获取观察"""
        return np.array([
            self.own_x,
            self.own_y, 
            self.own_heading,
            self.target_x,
            self.target_y,
            self.target_heading,
            self.distance,
            self.relative_bearing
        ], dtype=np.float32)
    
    def get_action_mask(self):
        """获取动作掩码"""
        has_collision_risk = self.distance < 4.0
        return self.colreg_rules.generate_action_mask(self.relative_bearing, has_collision_risk)
    
    def step(self, action):
        """执行动作"""
        # 应用动作（改变航向）
        heading_change = self.actions[action]
        self.own_heading = (self.own_heading + heading_change) % 360
        
        # 简单的奖励函数
        reward = self.calculate_reward(action)
        
        # 更新位置（简化）
        self.own_x += 0.1 * np.sin(np.radians(self.own_heading))
        self.own_y += 0.1 * np.cos(np.radians(self.own_heading))
        
        # 重新计算相对方位和距离
        dx = self.target_x - self.own_x
        dy = self.target_y - self.own_y
        angle_to_target = np.degrees(np.arctan2(dx, dy))
        self.relative_bearing = (angle_to_target - self.own_heading) % 360
        self.distance = np.sqrt(dx*dx + dy*dy)
        
        # 判断是否结束
        done = self.distance > 10.0 or self.distance < 0.5
        
        return self.get_observation(), reward, done
    
    def calculate_reward(self, action):
        """计算奖励"""
        # 获取当前动作掩码
        action_mask = self.get_action_mask()
        
        # 基础奖励
        reward = 0.1
        
        # 如果执行了被禁止的动作，给予负奖励
        if not action_mask[action]:
            reward -= 1.0  # 违反规则的重大惩罚
        
        # 根据距离给予奖励
        if self.distance > 2.0:
            reward += 0.5  # 保持安全距离
        
        # 根据会遇类型给予特定奖励
        rule_name, _, _ = self.colreg_rules.get_rule_info(self.relative_bearing, self.distance < 4.0)
        
        if rule_name == "Head-on" and action in [3, 4]:  # 对遇时右转
            reward += 0.5
        elif rule_name == "Crossing (Give-way)" and action in [3, 4]:  # 让路时右转
            reward += 0.5
        
        return reward

class SimplePolicy(nn.Module):
    """简单的策略网络"""
    
    def __init__(self, obs_dim, action_dim, hidden_dim=64):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim)
        )
    
    def forward(self, x):
        return self.network(x)

def apply_action_mask(logits, mask):
    """应用动作掩码"""
    masked_logits = logits.clone()
    mask_tensor = torch.tensor(mask, dtype=torch.bool, device=logits.device)
    # 确保mask的形状与logits匹配
    if len(mask_tensor.shape) == 1 and len(logits.shape) == 2:
        mask_tensor = mask_tensor.unsqueeze(0)
    masked_logits[~mask_tensor] = -1e10
    return masked_logits

def test_action_masking_effect():
    """测试动作遮蔽效果"""
    
    print("=== 动作遮蔽效果测试 ===\n")
    
    # 创建环境和策略
    env = MockShipEnvironment()
    policy = SimplePolicy(env.obs_dim, env.action_count)
    optimizer = optim.Adam(policy.parameters(), lr=0.01)
    
    # 测试参数
    num_episodes = 1000
    
    # 统计数据
    stats_with_mask = {"rewards": [], "rule_violations": [], "convergence_time": 0}
    stats_without_mask = {"rewards": [], "rule_violations": [], "convergence_time": 0}
    
    # 测试1：使用动作遮蔽
    print("测试1：使用动作遮蔽训练...")
    start_time = time.time()
    
    for episode in range(num_episodes):
        obs = env.reset()
        episode_reward = 0
        rule_violations = 0
        
        for step in range(50):  # 最多50步
            # 获取策略输出
            obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
            logits = policy(obs_tensor)
            
            # 应用动作遮蔽
            action_mask = env.get_action_mask()
            masked_logits = apply_action_mask(logits, action_mask)
            
            # 选择动作
            probs = torch.softmax(masked_logits, dim=-1)
            action = torch.multinomial(probs, 1).item()
            
            # 执行动作
            next_obs, reward, done = env.step(action)
            episode_reward += reward
            
            # 检查规则违反
            if not action_mask[action]:
                rule_violations += 1
            
            # 简单的策略梯度更新
            log_prob = torch.log(probs[0, action])
            loss = -log_prob * reward
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            obs = next_obs
            if done:
                break
        
        stats_with_mask["rewards"].append(episode_reward)
        stats_with_mask["rule_violations"].append(rule_violations)
        
        # 检查收敛（简单标准）
        if episode > 100 and np.mean(stats_with_mask["rewards"][-50:]) > 5.0 and stats_with_mask["convergence_time"] == 0:
            stats_with_mask["convergence_time"] = episode
    
    print(f"完成！用时: {time.time() - start_time:.2f}秒")
    
    # 重置策略
    policy = SimplePolicy(env.obs_dim, env.action_count)
    optimizer = optim.Adam(policy.parameters(), lr=0.01)
    
    # 测试2：不使用动作遮蔽
    print("测试2：不使用动作遮蔽训练...")
    start_time = time.time()
    
    for episode in range(num_episodes):
        obs = env.reset()
        episode_reward = 0
        rule_violations = 0
        
        for step in range(50):
            # 获取策略输出（不应用遮蔽）
            obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
            logits = policy(obs_tensor)
            
            # 直接选择动作（不使用遮蔽）
            probs = torch.softmax(logits, dim=-1)
            action = torch.multinomial(probs, 1).item()
            
            # 执行动作
            next_obs, reward, done = env.step(action)
            episode_reward += reward
            
            # 检查规则违反
            action_mask = env.get_action_mask()
            if not action_mask[action]:
                rule_violations += 1
            
            # 策略梯度更新
            log_prob = torch.log(probs[0, action])
            loss = -log_prob * reward
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            obs = next_obs
            if done:
                break
        
        stats_without_mask["rewards"].append(episode_reward)
        stats_without_mask["rule_violations"].append(rule_violations)
        
        # 检查收敛
        if episode > 100 and np.mean(stats_without_mask["rewards"][-50:]) > 5.0 and stats_without_mask["convergence_time"] == 0:
            stats_without_mask["convergence_time"] = episode
    
    print(f"完成！用时: {time.time() - start_time:.2f}秒")
    
    return stats_with_mask, stats_without_mask

def analyze_results(stats_with_mask, stats_without_mask):
    """分析测试结果"""
    
    print("\n=== 测试结果分析 ===")
    
    # 计算平均奖励
    avg_reward_with = np.mean(stats_with_mask["rewards"][-100:])
    avg_reward_without = np.mean(stats_without_mask["rewards"][-100:])
    
    # 计算规则违反次数
    total_violations_with = np.sum(stats_with_mask["rule_violations"])
    total_violations_without = np.sum(stats_without_mask["rule_violations"])
    
    # 收敛时间
    conv_time_with = stats_with_mask["convergence_time"]
    conv_time_without = stats_without_mask["convergence_time"]
    
    print(f"平均奖励 (最后100轮):")
    print(f"  使用动作遮蔽: {avg_reward_with:.2f}")
    print(f"  不使用遮蔽:   {avg_reward_without:.2f}")
    print(f"  提升: {((avg_reward_with - avg_reward_without) / avg_reward_without * 100):.1f}%")
    
    print(f"\n规则违反次数:")
    print(f"  使用动作遮蔽: {total_violations_with}")
    print(f"  不使用遮蔽:   {total_violations_without}")
    print(f"  减少: {((total_violations_without - total_violations_with) / total_violations_without * 100):.1f}%")
    
    print(f"\n收敛时间:")
    print(f"  使用动作遮蔽: {conv_time_with if conv_time_with > 0 else '未收敛'}")
    print(f"  不使用遮蔽:   {conv_time_without if conv_time_without > 0 else '未收敛'}")
    
    if conv_time_with > 0 and conv_time_without > 0:
        speedup = conv_time_without / conv_time_with
        print(f"  加速倍数: {speedup:.1f}x")

if __name__ == "__main__":
    # 运行测试
    stats_with, stats_without = test_action_masking_effect()
    
    # 分析结果
    analyze_results(stats_with, stats_without)
