import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取新数据文件
file_paths = [
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\CEPPO.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Linear Entropy.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Improved PPO by Entropy.xlsx',
    r'C:\Users\<USER>\Desktop\实验数据（终）\奖励图数据\Classic PPO.xlsx'
]

labels = ['CEPPO', 'Improved PPO by Linear Entropy', 'Improved PPO by Entropy', 'Classic PPO']

print("=== 新数据详细分析 ===\n")

datasets = []
for i, (file_path, label) in enumerate(zip(file_paths, labels)):
    try:
        data = pd.read_excel(file_path)
        datasets.append(data)
        
        print(f"--- {label} ---")
        print(f"数据形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
        
        if 'Step' in data.columns and 'Value' in data.columns:
            print(f"Step范围: {data['Step'].min()} - {data['Step'].max()}")
            print(f"Value范围: {data['Value'].min():.2f} - {data['Value'].max():.2f}")
            
            # 详细查看前10行数据
            print("前10行数据:")
            print(data[['Step', 'Value']].head(10))
            
            # 检查是否有重复的Step值
            step_counts = data['Step'].value_counts()
            duplicated_steps = step_counts[step_counts > 1]
            if len(duplicated_steps) > 0:
                print(f"⚠️ 发现重复的Step值: {duplicated_steps.to_dict()}")
                
                # 显示重复Step的详细信息
                for step_val in duplicated_steps.index[:3]:  # 只显示前3个重复的
                    duplicate_rows = data[data['Step'] == step_val]
                    print(f"Step={step_val}的所有行:")
                    print(duplicate_rows[['Step', 'Value']])
            
            # 检查Value=0的情况
            zero_values = data[data['Value'] == 0]
            if len(zero_values) > 0:
                print(f"Value=0的行数: {len(zero_values)}")
                print("Value=0的前5行:")
                print(zero_values[['Step', 'Value']].head())
        
        print("\n" + "="*60 + "\n")
        
    except Exception as e:
        print(f"读取 {label} 时出错: {e}\n")

# 如果成功读取所有数据，进行起点分析
if len(datasets) == 4:
    print("=== 起点问题诊断 ===")
    
    # 原始起点分析
    print("1. 原始数据起点:")
    for data, label in zip(datasets, labels):
        first_row = data.iloc[0]
        print(f"{label}: Step={first_row['Step']}, Value={first_row['Value']:.6f}")
    
    # 查找每个数据集的真正第一个非零值
    print("\n2. 第一个非零Value:")
    for data, label in zip(datasets, labels):
        non_zero_data = data[data['Value'] != 0]
        if len(non_zero_data) > 0:
            first_non_zero = non_zero_data.iloc[0]
            print(f"{label}: Step={first_non_zero['Step']}, Value={first_non_zero['Value']:.6f}")
        else:
            print(f"{label}: 没有非零值")
    
    # 查找每个数据集Step>0的第一个值
    print("\n3. Step>0的第一个值:")
    for data, label in zip(datasets, labels):
        positive_step_data = data[data['Step'] > 0]
        if len(positive_step_data) > 0:
            first_positive_step = positive_step_data.iloc[0]
            print(f"{label}: Step={first_positive_step['Step']}, Value={first_positive_step['Value']:.6f}")
        else:
            print(f"{label}: 没有Step>0的数据")
    
    # 可视化前50个数据点
    plt.figure(figsize=(15, 10))
    
    colors = ['#e76f51', '#e8c56a', '#299d91', '#8bb17b']
    
    # 子图1: 原始数据前50个点
    plt.subplot(2, 2, 1)
    for data, label, color in zip(datasets, labels, colors):
        plot_data = data.head(50)
        plt.plot(plot_data['Step'], plot_data['Value'], 'o-', label=label, color=color, markersize=4)
    plt.title('前50个数据点')
    plt.xlabel('Step')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    # 子图2: 放大前20个点
    plt.subplot(2, 2, 2)
    for data, label, color in zip(datasets, labels, colors):
        plot_data = data.head(20)
        plt.plot(plot_data['Step'], plot_data['Value'], 'o-', label=label, color=color, markersize=6)
    plt.title('前20个数据点（放大）')
    plt.xlabel('Step')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    # 子图3: 只显示非零值的前30个点
    plt.subplot(2, 2, 3)
    for data, label, color in zip(datasets, labels, colors):
        non_zero_data = data[data['Value'] != 0].head(30)
        if len(non_zero_data) > 0:
            plt.plot(non_zero_data['Step'], non_zero_data['Value'], 'o-', label=label, color=color, markersize=4)
    plt.title('非零值前30个点')
    plt.xlabel('Step')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    # 子图4: Step分布直方图
    plt.subplot(2, 2, 4)
    for data, label, color in zip(datasets, labels, colors):
        first_10_steps = data['Step'].head(10).values
        plt.scatter([label]*len(first_10_steps), first_10_steps, color=color, alpha=0.7, s=50)
    plt.title('前10个Step值分布')
    plt.ylabel('Step')
    plt.xticks(rotation=45)
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('新数据分析.png', dpi=300, bbox_inches='tight')
    print("\n分析图表已保存为: 新数据分析.png")
    plt.show()

print("\n=== 建议的解决方案 ===")
print("1. 如果数据有重复的Step=0行，需要清理重复数据")
print("2. 如果起点Value不同，可以使用偏移调整")
print("3. 如果Step起点不同，需要对齐Step轴")
print("4. 建议使用相对变化量而不是绝对值进行比较")
