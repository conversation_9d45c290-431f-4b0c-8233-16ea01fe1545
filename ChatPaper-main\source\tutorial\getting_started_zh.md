# 入门指南

## 基础库安装
为了运行该项目，您需要在计算机上安装 `Python 3.9`，或者您可以从[此处](https://www.python.org/downloads/)下载它。其他版本的 `Python 3` 也可以使用。

对于操作系统，我们建议使用 `Windows 10` 或 `Ubuntu 20.04 LTS`。其他操作系统（如 `Mac OS`）也应该可以工作。

## 安装 ChatPaper
为了让一切工作正常，首先您需要克隆本仓库：

```bash
git clone https://github.com/kaixindelele/ChatPaper
```

然后，您需要安装依赖项：

```bash
pip install -r requirements.txt
```

至此，您已经完成了安装。接下来，您可以运行 `ChatPaper` 完成您的科研需求了。