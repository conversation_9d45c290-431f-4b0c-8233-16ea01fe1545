# Getting Started

## Prerequisites
To run this project, you need to have `Python 3.9` installed on your computer, or you can download it from [here](https://www.python.org/downloads/). Other versions of `Python 3` should also work.

For the operating system, we recommend using `Windows 10` or `Ubuntu 20.04 LTS`. Other operating systems (e.g., Mac OS) should also work.

## Installation

To make things work, firstly you need to clone the repository:

```bash
git clone https://github.com/kaixindelele/ChatPaper
```

Then, you need to install the dependencies:

```bash
pip install -r requirements.txt
```

That's it! You are ready to go!