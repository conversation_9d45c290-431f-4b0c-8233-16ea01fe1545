%% 
%% Copyright 2019-2024 Elsevier Ltd
%% 
%% This file is part of the 'CAS Bundle'.
%% --------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.3c of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.3c or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'CAS Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for cas-dc documentclass for 
%% double column output.

\documentclass[a4paper,fleqn]{cas-dc}
% If the frontmatter runs over more than one page
% use the longmktitle option.

%\documentclass[a4paper,fleqn,longmktitle]{cas-dc}

%\usepackage[numbers]{natbib}
%\usepackage[authoryear]{natbib}
\usepackage[authoryear]{natbib}
%\usepackage{amsmath}
%% The amssymb package provides various useful mathematical symbols
\usepackage{amssymb}
%% The amsmath package provides various useful equation environments.
\usepackage{amsmath}
%%%Author macros
\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
%%%

% Uncomment and use as if needed
%\newtheorem{theorem}{Theorem}
%\newdefinition{rmk}{Remark}
%\newproof{pf}{Proof}
%\newproof{pot}{Proof of Theorem \ref{thm}}

\begin{document}
%\setCJKfamilyfont{font01}{cwTeXFangSong}\zihao{6}
%\newcommand{\fonta}{\CJKfamily{font01}}
\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}

% Short title
\shorttitle{}    

% Short author
\shortauthors{}  

% Main title of the paper
\title [mode = title]{CEPPO: A Novel Cognitive Entropy Proximal Policy Optimization Algorithm for Autonomous Ship Collision Avoidance based on Deep Reinforcement Learning}  

% Title footnote mark
% eg: \tnotemark[1]
\tnotemark[1] 

% Title footnote 1.
% eg: \tnotetext[1]{Title footnote text}
\tnotetext[1]{} 

% First author
%
% Options: Use if required
% eg: \author[1,3]{Author Name}[type=editor,
%       style=chinese,
%       auid=000,
%       bioid=1,
%       prefix=Sir,
%       orcid=0000-0000-0000-0000,
%       facebook=<facebook id>,
%       twitter=<twitter id>,
%       linkedin=<linkedin id>,
%       gplus=<gplus id>]

\author[1]{}%[<options>]

% Corresponding author indication
\cormark[1]

% Footnote of the first author
\fnmark[1]

% Email id of the first author
\ead{}

% URL of the first author
\ead[url]{}

% Credit authorship
% eg: \credit{Conceptualization of this study, Methodology, Software}
\credit{}

% Address/affiliation
\affiliation[1]{organization={},
            addressline={}, 
            city={},
%          citysep={}, % Uncomment if no comma needed between city and postcode
            postcode={}, 
            state={},
            country={}}

\author[2]{}%[]

% Footnote of the second author
\fnmark[2]

% Email id of the second author
\ead{}

% URL of the second author
\ead[url]{}

% Credit authorship
\credit{}

% Address/affiliation
\affiliation[2]{organization={},
            addressline={}, 
            city={},
%          citysep={}, % Uncomment if no comma needed between city and postcode
            postcode={}, 
            state={},
            country={}}

% Corresponding author text
\cortext[1]{Corresponding author}

% Footnote text
\fntext[1]{}

% For a title note without a number/mark
%\nonumnote{}

% Here goes the abstract
\begin{abstract}
Due to the complexity of maritime environments and the high-dimensional nature of ship collision avoidance decisions, existing Deep Reinforcement Learning (DRL) algorithms exhibit significant deficiencies in balancing the exploration-exploitation trade-off in policy learning. Insufficient exploration in the early training stages causes the agent to prematurely converge to suboptimal solutions, thereby directly affecting the convergence rate and decision-making performance of the intelligent agent. In response to the aforementioned challenge, this research proposes a Cognitive Entropy-based Proximal Policy Optimization algorithm（CEPPO）. This algorithm optimizes the training process through a three-stage dynamic adjustment mechanism: in the early training phase, it enhances exploration capability to effectively avoid local optima, while introducing entropy regularization and reward normalization mechanisms to reduce policy gradient variance; in the mid-training phase, it adopts adaptive balancing of exploration and exploitation to accelerate algorithm convergence; in the late training phase, it gradually decreases exploration to ensure policy stability and generalization capabilities. Additionally, this paper utilizes the Unreal Engine to construct a simulation platform for simulating complex dynamic scenarios. It also designs a multi-layered reward mechanism to further optimize collision avoidance strategies. 
\end{abstract}

% Use if graphical abstract is present
%\begin{graphicalabstract}
%\includegraphics{}
%\end{graphicalabstract}

% Research highlights
\begin{highlights}
\item 
\item 
\item 
\end{highlights}


%\nocite{*}

% Keywords
% Each keyword is seperated by \sep
\begin{keywords}
Deep Reinforcement Learning \sep autonomous ship collision avoidance \sep cognitive entropy \sep Proximal Policy Optimization (PPO) \sep  COLREGs
\end{keywords}

\maketitle

% Main text
\section{Introduction}\label{}
With the rapid development of the maritime economy and intelligent navigation, unmanned ship technology is increasingly becoming the focus of the shipping industry. Unmanned ships have advantages such as all-weather operation, remote control, and intelligent autonomous decision-making, which can effectively reduce labor costs and enhance maritime safety and efficiency. However, unmanned ships still face numerous technical challenges during actual operations, with collision avoidance being particularly prominent. The problem of collision avoidance in unmanned ships encompasses not only the safety of navigational operations but also the compliance with maritime traffic regulations and the ability to make intelligent decisions in complex settings. This represents a critical challenge that urgently demands resolution within the advanced domain of autonomous maritime transport.

Currently, there have been numerous research achievements in the field of autonomous collision avoidance for unmanned ships. Traditional collision avoidance algorithms such as the A* algorithm, artificial potential field method, and genetic algorithms have been widely applied. Langbein (2010) \citep{ref1} and Li (2017) \citep{ref2} each proposed path planning methods based on an improved A* algorithm, which enhanced the smoothness of the paths. However, they still faced issues such as excessive turning points and redundant collision assessments. In response, Sun et al. (2023) significantly enhanced the practical applicability of their algorithm by incorporating ant colony optimization (ACO) with adaptive step size and bidirectional cooperation strategies \citep{ref3}. However, this approach still falls short in autonomous decision-making and dynamic collision avoidance in unmanned boats.

In terms of autonomous collision avoidance for unmanned ships, Goodwin et al. (1975) first introduced the domain model for ships by statistically analyzing the behavior of vessels in open waters \citep{ref4}. This model divides the area around a ship into multiple virtual safety zones, triggering collision avoidance maneuvers when obstacles enter these zones. It uses CPA (Closest Point of Approach), DCPA (Distance at Closest Point of Approach), and TCPA (Time to Closest Point of Approach) to assess the spatial and temporal dimensions of collision risk. Building on this foundation, Mou et al. (2010) developed a dynamic risk assessment method based on linear regression using AIS data \citep{ref5}, which incorporates correlations between CPA and factors such as ship size, speed, and course, though still not achieving a quantitative evaluation of the collision risk index. Zhen et al. (2017) quantified the degree of collision risk through cluster analysis based on DSCBN clustering analysis and AIS data \citep{ref6}. However, with the increasing complexity of modern maritime systems, many challenges arise in forming comprehensive collision avoidance models \citep{ref7}, leading to significant uncertainties in the practical application of model-based algorithms.

With the development of artificial intelligence, intelligent algorithms represented by reinforcement learning have gained widespread attention in the field of ship collision avoidance. Model-free reinforcement learning, due to its simple structure and suitability for complex systems \citep{ref8}, has been widely applied in autonomous navigation. Yang et al. (2014) proposed a path planning method for unmanned ships based on Q-learning \citep{ref9}, which comprehensively considers ship motion models and maneuvering characteristics, obtaining the optimal strategy through learning the action-state model. However, traditional reinforcement learning algorithms typically require the construction and maintenance of a state-value function table, which is inefficient in high-dimensional action-state spaces and can even lead to state-space explosion issues. Additionally, due to the limited perceptual capabilities of reinforcement learning algorithms towards the environment, it is challenging to fully explore all possible action-state information. To address this, researchers have introduced deep neural networks to improve reinforcement learning algorithms, resulting in Deep Reinforcement Learning (DRL) \citep{ref10}. DRL combines the perceptual abilities of Deep Learning (DL) with the decision-making capabilities of Reinforcement Learning (RL) \citep{ref11}. Deep learning provides a learning objective for reinforcement learning, while reinforcement learning furnishes a learning mechanism for deep learning, thereby making deep reinforcement learning more adaptable to complex control strategies.

Currently, numerous researchers have explored the application of Deep Reinforcement Learning (DRL) in path planning and have gradually applied it to autonomous collision avoidance for ships \citep{ref12}. Zhao and colleagues proposed a path planning method using the Deep Deterministic Policy Gradient (DDPG) algorithm \citep{ref13}, utilizing AIS data to train the DRL model, which exhibits good convergence speed and stability. However, the absence of a ship motion model in the analysis weakens the stability when addressing practical issues. Zhou and his team designed a semi-Markov decision model and neural network architecture based on the DQN algorithm for the USV collision avoidance problem \citep{ref14}. Experimental results indicate this method effectively solves multi-ship collision avoidance issues, but the use of visual image data as input for DQN results in high computational demands, slowing convergence speeds. Yuan and colleagues introduced a path generation method based on DRL, combining ship domains and CPA for collision risk assessment and producing collision avoidance paths in compliance with COLREGs \citep{ref15}. Recently, a predictive-decision joint collision avoidance algorithm based on DDPG was introduced \citep{ref16}. It employs a dual-layer state-space design, integrating the Velocity Obstacle (VO) model to predict potential collision zones and optimizing training efficiency and collision avoidance strategies through a dense reward mechanism. The algorithm's performance in typical scenarios and complex multi-ship environments has been validated on the Unity3D simulation platform and in real tests, demonstrating excellent safety, stability, and practical value.

However, due to the complexity of the maritime collision avoidance environment, the existing methods still have significant shortcomings in balancing exploration and exploitation in collision avoidance strategies, leading to either insufficient exploration or slow convergence, thus affecting the global optimization capabilities. To address this issue, Lai et al. \citep{ref17} introduced an entropy regularization strategy to enhance exploratory capabilities, but due to the fixed entropy coefficient settings, there are still problems with insufficient exploration at the beginning of training or oscillating convergence later on. Chen et al. proposed a Dynamically Adjusted Entropy Proximal Policy Optimization (DAE-PPO) algorithm \citep{ref18}, which uses a quadratic decrement entropy method to optimize the exploration mechanism, thereby further improving the performance of exploration and exploitation in the strategy.

This paper introduces an innovative Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm, in which the change in entropy depends not only on training time but also on the model's cognition for nonlinear adjustments. This approach achieves a dynamic balance between exploration and exploitation, significantly enhancing the training efficiency, convergence speed, and global optimization capabilities of the collision avoidance strategy. Specifically, at the initial stage of training, unmanned boats have an imperfect understanding of the environment, and CEPPO adopts a higher entropy value to guide the agent in extensive exploration, avoiding falling into local optima. As the training progresses and the agent's cognitive abilities gradually improve, CEPPO dynamically adjusts the entropy value according to the model's learning progress, achieving an optimal balance between exploration and exploitation and accelerating the convergence speed. In the later stages of training, as the agent's understanding of the environment stabilizes, the entropy value is further reduced to enhance the stability and global optimality of the strategy.

This innovation allows the algorithm to adaptively perceive its own learning state and adjust its exploration strategy, thereby achieving more efficient and robust collision avoidance decision optimization in complex dynamic environments.

The main contributions of this study include:
\begin{enumerate}[(1)]
\item A cognitive entropy model is proposed, capable of flexibly controlling exploratory behavior based on the progress of learning about the environment, achieving adaptive control of unmanned boat collision avoidance strategy exploration.
\item A new Cognitive Entropy Proximal Policy Optimization (CEPPO) algorithm based on the PPO algorithm is proposed, allowing unmanned boats to dynamically adjust their exploration strategies according to their cognitive state during the learning process of collision avoidance strategies. This optimization balances exploration and exploitation, achieving efficient and stable collision avoidance decision learning in complex maritime environments.
\item A high-fidelity simulation platform based on Unreal Engine 5 (UE5) is constructed, comprehensively verifying the robustness and adaptability of the proposed algorithm in complex marine environments through simulation of dynamic multi-ship intersecting scenarios.
\end{enumerate}

The rest of this paper is organized as follows: Section 2 reviews related research; Section 3 introduces the basic framework of the ship collision avoidance system; Section 4 provides a detailed description of the CEPPO algorithm and the designed reward functions; experimental design and simulation results are presented in Section 5; finally, Section 6 summarizes the research contributions and discusses future work directions.

% Figure
\begin{figure}%[]
  \centering
    \includegraphics[width=.3\columnwidth]{figs/111.png}
    \caption{Specific}\label{fig1}
\end{figure}


\begin{table}%[]
\caption{}\label{tbl1}
\begin{tabular*}{\tblwidth}{@{}LL@{}}
\toprule
  &  \\ % Table header row
\midrule
 & \\
 & \\
 & \\
 & \\
\bottomrule
\end{tabular*}
\end{table}

% Uncomment and use as the case may be
%\begin{theorem} 
%\end{theorem}

% Uncomment and use as the case may be
%\begin{lemma} 
%\end{lemma}

%% The Appendices part is started with the command \appendix;
%% appendix sections are then done as normal sections
%% \appendix

\section{Problem Formulation}\label{}
In the path planning of Unmanned Surface Vessels (USVs), it is essential to thoroughly consider the diversity and complexity of the marine environment, such as the undulating terrain of waterways, variations in ocean currents, uncertainties in wind speed and direction, and other environmental interference factors. Additionally, strict adherence to the International Regulations for Preventing Collisions at Sea (COLREGs) established by the International Maritime Organization (IMO) is mandatory. Ensuring the adaptability and accuracy of the dynamic motion model of USVs under these conditions is crucial. These environmental variables significantly increase the difficulty of path planning, requiring the system to respond in real-time and adjust the course to optimize the voyage.

Firstly, compliance with the COLREGs is vital for ensuring the safe interaction of USVs with other maritime vessels. These rules specify the behaviors of vessels in different maritime encounter situations, such as head-on, crossing, and overtaking scenarios. The autonomous driving system of USVs must be capable of recognizing these situations and automatically taking appropriate collision avoidance measures, such as altering speed or adjusting course, to avoid potential collision risks.

Secondly, considering environmental factors, path planning must account for the impact of weather changes, ocean currents, and waves. For example, strong winds may affect the stability and course accuracy of USVs, while strong currents may cause USVs to deviate from the predetermined route. Additionally, underwater obstacles and shallow areas in the waterway terrain must be considered to ensure USVs do not run aground due to terrain obstacles. The dynamic motion model of USVs must not only simulate the physical response of USVs in the actual marine environment, such as acceleration, steering, and docking but also adapt to the influence of environmental factors and operational rules constraints.

This study focuses on the path planning of Unmanned Surface Vessels (USVs) based on deep reinforcement learning. To develop an effective and robust navigation system, it is crucial to integrate the principles of the COLREGs and accurately simulate the dynamic behavior of USVs under various environmental conditions. This section will elaborate on the fundamental framework essential for achieving safe and efficient operational capabilities in complex marine environments.

\subsection{COLREGs}
In the path planning of Unmanned Surface Vessels (USVs), in addition to optimizing the navigation path (e.g., shortest distance, least time, or lowest energy consumption), it is imperative to strictly adhere to the International Regulations for Preventing Collisions at Sea (COLREGs) established by the International Maritime Organization (IMO). COLREGs aim to ensure maritime safety by prescribing navigation behaviors and collision avoidance strategies to reduce the occurrence of collisions. The following aspects are particularly noteworthy:

1. Head-On Situation (Rule 14):
   When two power-driven vessels are meeting on reciprocal or nearly reciprocal courses, both should alter their course to starboard so that each vessel passes on the port side of the other. This means that in path planning, the USV must detect any oncoming vessels and adjust its course in advance to comply with this rule.

2. Crossing Situation (Rule 15):
   When two power-driven vessels are crossing so as to involve the risk of collision, the vessel which has the other on her starboard side shall keep out of the way and shall avoid crossing ahead of the other vessel. The path planning algorithm must identify vessels approaching from the starboard side and adjust the USV's path accordingly to yield.

3. Action by Give-Way Vessel (Rule 16):
   Every vessel which is directed to keep out of the way of another vessel shall, so far as possible, take early and substantial action to keep well clear. The USV's path planning should include early detection and avoidance functions to ensure it can promptly and effectively change its course and speed when necessary.

4. Responsibilities Between Vessels (Rule 18):
   In specific situations, such as in narrow channels, power-driven vessels should give way to vessels restricted in their ability to maneuver. The USV must account for these right-of-way rules in its path planning to ensure compliance with avoidance obligations in complex navigation environments.

By integrating COLREGs into the path planning algorithm, USVs can not only accomplish their missions efficiently but also ensure navigational safety and avoid collisions with other vessels. This is crucial for advancing the application of USVs in fields such as maritime transportation, patrolling, and environmental monitoring.

\subsection{Dynamic Motion Model of USVs}
In the actual marine environment, the motion of a vessel can be considered as a combination of translational and rotational movements (Wang et al., 2019; Qiao and Zhang, 2019; Yi et al., 2020). The nonlinear dynamic equations can be represented as follows:

\begin{equation}
\dot{\eta} = T(\psi) \nu
\end{equation}
\begin{equation}
M\dot{\nu} = N(\nu)\nu + g(\nu) + \tau + \tau_w
\end{equation}

\iffalse
\begin{equation}
\begin{split}
\label{deqn_ex1a}
M \dot{\nu} = N(\nu)\nu + g(\nu) + \tau + \tau_w
\end{split}
\end{equation}
\fi

where $T(\psi) $ is the transformation matrix, satisfying \( T^{-1}(\psi)T(\psi) = I_{3\times3} \). The position vector \( \eta = [x, y, \psi]^T \in \mathbb{R}^3 \), and the velocity vector \( \nu = [u, v, r]^T \in \mathbb{R}^3 \), where \( u \) is the longitudinal velocity, \( v \) is the lateral velocity, and \( r \) is the yaw rate.

The inertia matrix
\begin{equation}
M = M^T = M_A + M_{RB} > 0
\end{equation}

is the inertia matrix, where \( m \) is the mass of the vessel, \( I_z \) is the rotational inertia about the z-axis. \( X_{\dot{u}}, Y_{\dot{v}}, Y_{\dot{r}}, N_{\dot{v}}, N_{\dot{r}} \) are added mass coefficients.

Hydrodynamic forces

\[ N(\nu)\nu = C(\nu)\nu + D(\nu) \]

where \( C(\nu) = C_A(\nu) + C_{RB}(\nu) \) represents hydrodynamic forces. Table 1 lists the hydrodynamic coefficients \( X_u, X_{|u|u}, X_{uuu}, Y_r, Y_v, Y_{|v|v}, Y_{|r|v}, Y_{|v|r}, Y_{|r|r}, N_r, N_v, N_{|v|r}, N_{|r|v}, N_{|v|v}, N_{|r|r} \).

Unmodeled dynamics

\[ g(\nu) = [g_u, g_v, g_r]^T \in \mathbb{R}^3 \]

refer to unmodeled dynamics parameters as described in Xu et al., 2020a.

Control input vector

\[ \tau = [\tau_u, \tau_{\nu}, \tau_r]^T \in \mathbb{R}^3 \]

where \( \tau_u \) is the thrust, \( \tau_r \) is the moment, and \( \tau_r = \frac{1}{2}C_L\rho AV^2L \). \( C_L \) is the lift coefficient, depending on the geometry of the rudder blade and varying with the rudder angle \( \delta \). \( \rho \) is the water density, \( A \) is the rudder area, \( V \) is the water flow velocity at the rudder, and \( L \) is the distance from the rudder's center of pressure to the USV's center. For underactuated USVs, \( \tau_{\nu} = 0 \).

Environmental disturbance vector

\[ \tau_w = [\tau_{wu}, \tau_{wv}, \tau_{wr}]^T \in \mathbb{R}^3 \]

is the time-varying environmental disturbance vector (see Table 1). 

In summary, the dynamic motion model of USVs comprehensively considers changes in position and velocity, hydrodynamic forces, unmodeled dynamics, control inputs, and environmental disturbances, providing a theoretical foundation for the path planning and control of USVs in complex marine environments.

\section{Proposed Neuro-Symbolic Path Planning Approach}\label{}
\subsection{Symbolic Representation of COLREGs}
（描述规则）
In this section, we use first-order symbolic logic to express key rules from the International Regulations for Preventing Collisions at Sea (COLREGs). Formal representation allows USV path planning algorithms to better understand and execute these rules.

1.**Overtaking Situation(Rule13)**

Condition: Assuming that the Target ship (TS) appears within (112.5°, 247.5°) azimuth of the Own ship (OS), the situation should be judged as an overrun situation.

Rule: When any ship overtakes another ship, the overtaking ship has to act as a give way ship, while the ship being overtaken as a straight ship can keep its speed and direction.

Symbolic logic expression:

Condition: \textbf{Overtaking(OS,TS)⇐Bearing(OS,TS,θ)∧112.5∘≤θ≤247.5∘}}

Rule:\textbf{Give\_Way(OS)∧Keep\_Course\_Speed(TS)⇐Overtaking(OS,TS)}}

2. **Head-On Situation (Rule 14)**

Condition:  Assuming that OS and TS meet on opposite or nearly opposite routes within the azimuths of (0°, 5°) or (355°, 360°), the situation should be judged as a head-to-head situation.

Rule: These two ships (OS and TS) should change course to starboard so that each ship can pass to the port side of the other to avoid collision.

Symbolic logic expression:

Condition: \textbf{Head\_On(OS,TS)⇐(Bearing(OS,TS,θ)∧(0∘≤θ≤5∘))∨(Bearing(OS,TS,θ)∧(355∘≤θ≤360∘))∧Opposite\_Course(OS,TS)}}

Rule:\textbf{Turn\_Right(OS)∧Turn\_Right(TS)⇐Head\_On(OS,TS)}}

3. **Crossing Situation (Rule 15)**

3.1 Crossing on the Port Side

Condition: If the target ship (TS) appears in the bearing angle of the own ship (OS) between 247.5° and 355°, it should be considered a crossing on the port side situation. 

Rule: OS, as the stand-on vessel, may maintain its course and speed, while TS, as the give-way vessel, needs to alter its course to the right. 

Symbolic logic expression:

Condition: \textbf{\textbf{Cross\_Port(OS,TS)⇐Bearing(OS,TS,θ)∧247.5∘≤θ≤355∘}}

Rule:\textbf{\textbf{Keep\_Course\_Speed(OS)∧Turn\_Right(TS)⇐Cross\_Port(OS,TS)}}}}

3.2 Crossing on the Starboard Side 

Condition: If the target ship (TS) appears in the bearing angle of the own ship (OS) between 5° and 112.5°, it should be considered a crossing on the starboard side situation. 

Rule:TS, as the stand-on vessel, may maintain its course and speed, while OS, as the give-way vessel, needs to alter its course to the right. Additionally, if the sailing conditions allow, OS should avoid crossing ahead of TS. 

Symbolic logic expression:

Condition:\textbf{\textbf{Cross\_Starboard(OS,TS)⇐Bearing(OS,TS,θ)∧5∘≤θ≤112.5∘}}}}

Rule:\textbf{\textbf{Keep\_Course\_Speed(TS)∧Turn\_Right(OS)∧Avoid\_Cross\_Ahead(OS)⇐Cross\_Starboard(OS,TS)}}

4. **Action by stand-on vessel (Rule 17)**

When one of the two vessels shall give way to the other, the other vessel shall maintain course and speed.

Symbolic logic expression:

Keep\_Course\_Speed(OS)⇐Give\_Way(TS) 

4.1 Dangerous situation

A hazardous situation is recognized when the OS detects that the TS is not taking action and the risk of collision is present.

Symbolic logic expression:

Condition: Danger\_Situation(OS,TS)⇐Keep\_Course\_Speed(OS)∧Collision\_Risk(OS,TS)∧¬Action\_Taken(TS)

Rule:Avoid\_Collision\_Action(OS)⇐Danger\_Situation(OS,TS) 

4.2 Urgent situation

A situation of urgency is judged when the OS finds that a collision cannot be avoided even if the TS takes action.

Symbolic logic expression:

Condition:Imminent\_Danger(OS,TS)⇐Imminent\_Collision(OS,TS)∧¬Sufficient\_Action\_Taken(TS) 

Rule:Take\_Emergency\_Action(OS)⇐Imminent\_Danger(OS,TS) 

4.3Motorized vessels avoid turning to port

If in a cross-encounter situation and the target vessel (TS) is on the port side of the home vessel (OS), a straight ahead vessel should not turn to the left.

Symbolic logic expression:

¬Turn\_Left(OS)⇐Crossing\_Situation(OS,TS)∧Ship\_On\_Port(TS) 

By formalizing these rules into logical expressions, we can more clearly define the behavioral guidelines that USVs must follow in order to comply with COLREGs, thus creating a library of rule-based masking behaviors for subsequent masking operations.

\subsection{The Architecture of Neuro-Symbolic Path Planning Model}
Model
\subsection{Rule-based Reinforcement Learning Algorithm}
Action Masking
（公式）
When conducting path planning for Unmanned Surface Vehicles (USVs), collision avoidance rules significantly influence the actual exploration space. Although theoretically, the exploration space of USVs could cover the entire ocean, such complete exploration is impractical and may pose safety risks in real-world operations. Collision avoidance rules require USVs to adhere to the International Maritime Organization (IMO) regulations and navigation standards during path planning. Therefore, USVs do not need to fully explore the ocean space but should conduct selective exploration based on collision avoidance rules.

The importance of action masking lies in its ability to effectively reduce the selection of invalid actions and dangerous paths, thus enhancing the efficiency and safety of path planning. Action masking means excluding actions that may lead to collisions from the decision space during the path planning process, ensuring that USVs only plan paths within safe and compliant areas. This not only reduces computational complexity but also improves the practical feasibility and safety of path planning. 

Specifically, the action masking technique can solve the following problems when performing path planning for USVs:1.Collision Risk: During the path planning process, USVs may select actions that will lead to collisions. Action masking reduces the risk of collision by filtering out these invalid actions.2.Computational Complexity: Without motion masking, USVs need to evaluate a large number of possible paths, which incurs significant computational overhead. Action masking reduces computational complexity by reducing the decision space. 3.Path validity: Action masking ensures that USVs only consider valid paths that comply with collision avoidance rules, improving the validity and accuracy of path planning.

In strategy gradient algorithms, strategies are usually represented using a neural network that outputs a non-normalised score, which is then converted to an action probability distribution by a Softmax operation. If an action ai is invalid for state si, action masking makes the sampling probability of the invalid action close to 0 by replacing the logits corresponding to the invalid action with a large complex number M. Updating the policy gradient using the masked probability distribution ensures that the gradient computation is based only on the probability distribution of the valid action.

In the previous section, first-order symbolic logic was used to express maritime collision avoidance rules, providing a foundation for the masked PPO algorithm utilized in this section. The following defines the masked PPO algorithm: 
\begin{figure}
    \centering
    \includegraphics[width=0.5\linewidth]{联想截图_20241008164619.png}
    \caption{Maskable PPO}
    \label{fig:enter-label}
\end{figure}
\section{Experimental Results and Analysis}\label{}
We perform experiments primarily to evaluate 

\section{Conclusion and Future Work}\label{}

% To print the credit authorship contribution details
\printcredits

%% Loading bibliography style file
%\bibliographystyle{model1-num-names}
\bibliographystyle{cas-model2-names}

% Loading bibliography database
\bibliography{refs}

% Biography
%\bio{}
% Here goes the biography details.
%\endbio

%\bio{pic1}
% Here goes the biography details.
%\endbio

\end{document}

