# -*- coding: utf-8 -*-
'''
COLREG避碰规则引擎
用于船舶避碰训练中的动作遮蔽

基于国际海上避碰规则(COLREGs)实现精确的动作掩码生成
'''

import numpy as np
from typing import Tuple, Optional

class COLREGRules:
    """
    COLREG避碰规则引擎
    实现国际海上避碰规则的动作遮蔽逻辑
    """
    
    # 船舶动作定义
    ACTION_HARD_LEFT = 0      # 大幅左转
    ACTION_SLIGHT_LEFT = 1    # 小幅左转  
    ACTION_MAINTAIN = 2       # 保持航向
    ACTION_SLIGHT_RIGHT = 3   # 小幅右转
    ACTION_HARD_RIGHT = 4     # 大幅右转
    
    def __init__(self, action_count: int = 5):
        """
        初始化规则引擎
        
        参数:
        - action_count: 动作总数
        """
        self.action_count = action_count
    
    def normalize_bearing(self, bearing: float) -> float:
        """将方位角标准化到0-360度范围"""
        while bearing < 0:
            bearing += 360
        while bearing >= 360:
            bearing -= 360
        return bearing
    
    def is_head_on_situation(self, relative_bearing: float) -> bool:
        """
        判断是否为对遇情况 (COLREGs Rule 14)
        
        参数:
        - relative_bearing: 目标船相对方位角 (0-360度)
        
        返回:
        - bool: 是否为对遇情况
        """
        bearing = self.normalize_bearing(relative_bearing)
        return (bearing >= 350 and bearing <= 360) or (bearing >= 0 and bearing <= 10)
    
    def is_crossing_give_way(self, relative_bearing: float) -> bool:
        """
        判断是否为交叉相遇让路船情况 (COLREGs Rule 15)

        参数:
        - relative_bearing: 目标船相对方位角 (0-360度)

        返回:
        - bool: 是否为让路船
        """
        bearing = self.normalize_bearing(relative_bearing)
        return bearing >= 5 and bearing < 112.5  # 修正边界，112.5度属于追越
    
    def is_crossing_stand_on(self, relative_bearing: float) -> bool:
        """
        判断是否为交叉相遇直行船情况 (COLREGs Rule 17)
        
        参数:
        - relative_bearing: 目标船相对方位角 (0-360度)
        
        返回:
        - bool: 是否为直行船
        """
        bearing = self.normalize_bearing(relative_bearing)
        return bearing >= 247.5 and bearing <= 355
    
    def is_overtaking_situation(self, relative_bearing: float) -> bool:
        """
        判断是否为追越情况 (COLREGs Rule 13)
        
        参数:
        - relative_bearing: 目标船相对方位角 (0-360度)
        
        返回:
        - bool: 是否为追越情况
        """
        bearing = self.normalize_bearing(relative_bearing)
        return bearing >= 112.5 and bearing <= 247.5
    
    def generate_action_mask(self, relative_bearing: float, has_collision_risk: bool = True,
                           strictness_level: str = "normal") -> np.ndarray:
        """
        根据COLREG规则生成动作掩码

        参数:
        - relative_bearing: 目标船相对方位角 (0-360度)
        - has_collision_risk: 是否存在碰撞风险
        - strictness_level: 严格程度 ("strict", "normal", "flexible", "emergency")

        返回:
        - np.ndarray: 动作掩码，True表示允许的动作
        """
        
        # 如果没有碰撞风险，所有动作都允许
        if not has_collision_risk:
            return np.ones(self.action_count, dtype=bool)

        # 紧急情况：允许所有动作
        if strictness_level == "emergency":
            return np.ones(self.action_count, dtype=bool)

        # 默认所有动作都允许
        mask = np.ones(self.action_count, dtype=bool)
        
        if self.is_head_on_situation(relative_bearing):
            # Rule 14: 对遇情况 - 两船均应右转避让
            if strictness_level in ["strict", "normal"]:
                mask[self.ACTION_HARD_LEFT] = False
                mask[self.ACTION_SLIGHT_LEFT] = False
                mask[self.ACTION_MAINTAIN] = False  # 对遇时不应保持航向
            elif strictness_level == "flexible":
                mask[self.ACTION_HARD_LEFT] = False  # 只禁止大幅左转
            
        elif self.is_crossing_give_way(relative_bearing):
            # Rule 15: 交叉相遇（让路船）- 目标船在右舷，本船应右转避让
            if strictness_level in ["strict", "normal"]:
                mask[self.ACTION_HARD_LEFT] = False
                mask[self.ACTION_SLIGHT_LEFT] = False
                mask[self.ACTION_MAINTAIN] = False  # 让路船不能保持航向
            elif strictness_level == "flexible":
                mask[self.ACTION_HARD_LEFT] = False  # 只禁止大幅左转
            
        elif self.is_crossing_stand_on(relative_bearing):
            # Rule 17: 交叉相遇（直行船）- 目标船在左舷，本船应保持航向
            # 通常保持所有动作可用，但在实际应用中可能需要根据具体情况调整
            pass
            
        elif self.is_overtaking_situation(relative_bearing):
            # Rule 13: 追越情况 - 本船为追越船，必须避让
            mask[self.ACTION_MAINTAIN] = False  # 追越船不能保持航向
            
        return mask
    
    def get_rule_info(self, relative_bearing: float, has_collision_risk: bool = True) -> Tuple[str, str, int]:
        """
        获取适用的规则信息
        
        参数:
        - relative_bearing: 目标船相对方位角 (0-360度)
        - has_collision_risk: 是否存在碰撞风险
        
        返回:
        - Tuple[str, str, int]: (规则名称, 规则描述, 规则编号)
        """
        
        if not has_collision_risk:
            return "Safe", "无碰撞风险", 0
        
        if self.is_head_on_situation(relative_bearing):
            return "Head-on", "对遇情况 - 两船均应右转避让", 14
            
        elif self.is_crossing_give_way(relative_bearing):
            return "Crossing (Give-way)", "交叉相遇 - 本船为让路船，应右转避让", 15
            
        elif self.is_crossing_stand_on(relative_bearing):
            return "Crossing (Stand-on)", "交叉相遇 - 本船为直行船，应保持航向", 17
            
        elif self.is_overtaking_situation(relative_bearing):
            return "Overtaking", "追越情况 - 本船为追越船，必须避让", 13
            
        else:
            return "Unknown", "未知情况", 0
    
    def apply_mask_to_action_distribution(self, action_logits: np.ndarray, action_mask: np.ndarray) -> np.ndarray:
        """
        将动作掩码应用到动作分布上
        
        参数:
        - action_logits: 动作对数概率分布
        - action_mask: 动作掩码
        
        返回:
        - np.ndarray: 应用掩码后的动作分布
        """
        masked_logits = action_logits.copy()
        masked_logits[~action_mask] = -1e10  # 将被禁止的动作概率设为极小值
        return masked_logits

# 使用示例和测试函数
def test_colreg_rules():
    """测试COLREG规则引擎"""
    rules = COLREGRules()
    
    print("=== COLREG规则引擎测试 ===\n")
    
    # 测试各种方位角
    test_cases = [
        (0, "正前方 - 对遇"),
        (5, "右舷前方 - 对遇边界"),
        (45, "右舷 - 交叉相遇(让路)"),
        (90, "正右舷 - 交叉相遇(让路)"),
        (112.5, "右舷后方 - 追越边界"),
        (180, "正后方 - 追越"),
        (247.5, "左舷后方 - 追越边界"),
        (270, "正左舷 - 交叉相遇(直行)"),
        (300, "左舷前方 - 交叉相遇(直行)"),
        (350, "左舷前方 - 对遇边界"),
    ]
    
    action_names = ["大幅左转", "小幅左转", "保持航向", "小幅右转", "大幅右转"]
    
    for bearing, description in test_cases:
        print(f"方位角 {bearing}° - {description}")
        
        # 获取规则信息
        rule_name, rule_desc, rule_number = rules.get_rule_info(bearing, True)
        print(f"  适用规则: Rule {rule_number} - {rule_desc}")
        
        # 生成动作掩码
        mask = rules.generate_action_mask(bearing, True)
        
        # 显示允许的动作
        allowed_actions = [action_names[i] for i, allowed in enumerate(mask) if allowed]
        print(f"  允许动作: {', '.join(allowed_actions)}")
        print()

if __name__ == "__main__":
    test_colreg_rules()
