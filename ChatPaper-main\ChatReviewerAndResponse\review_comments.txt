#1 Reviewer

Overall Review:
The paper proposes a novel Coarse-to-fine Cascaded Evidence-Distillation (CofCED) neural network for explainable fake news detection. The proposed model selects the most explainable sentences for verdicts based on raw reports, thereby reducing the dependency on fact-checked reports. The paper presents two explainable fake news datasets and experimental results demonstrating that the proposed model outperforms state-of-the-art detection baselines and generates high-quality explanations.

Paper Strength:
(1) The paper addresses an important and timely problem of fake news detection and provide insights into the limitations of existing methods.
(2) The proposed CofCED model is innovative and utilizes a hierarchical encoder and cascaded selectors for selecting explainable sentences. 
(3) The paper contributes to the research community by presenting two publicly available datasets for explainable fake news detection.

Paper Weakness:
(1) The paper could benefit from more detailed clarification of the proposed model's architecture and implementation details.
(2) The paper lacks comparison with more relevant and widely-used baseline methods in the field.
(3) Although the paper constructs two explainable fake news datasets, the paper does not describe the process and criteria for creating them.

Questions To Authors And Suggestions For Rebuttal:
(1) Can the authors provide additional information on the proposed model's architecture and implementation details?
(2) Can the authors compare their proposed method with additional relevant and widely-used baseline methods in the field?
(3) Can the authors provide more details on the process and criteria for creating the two constructed explainable fake news datasets?

Overall score (1-5): 4
The paper provides an innovative approach to fake news detection using a cascade of selectors and presents two publicly available datasets for the research community. However, the paper could benefit from additional details on architectural and implementation details and comparisons with more relevant baselines.

#2 Reviewer

Overall Review:
The paper proposes a novel Coarse-to-fine Cascaded Evidence-Distillation (CofCED) neural network for explainable fake news detection. The proposed model selects the most explainable sentences for verdicts based on raw reports, thereby reducing the dependency on fact-checked reports. The paper presents two explainable fake news datasets and experimental results demonstrating that the proposed model outperforms state-of-the-art detection baselines and generates high-quality explanations.

Paper Strength:
(1) The paper addresses an important and timely problem of fake news detection and provide insights into the limitations of existing methods.
(2) The proposed CofCED model is innovative and utilizes a hierarchical encoder and cascaded selectors for selecting explainable sentences. 
(3) The paper contributes to the research community by presenting two publicly available datasets for explainable fake news detection.

Paper Weakness:
(1) The paper could benefit from more detailed clarification of the proposed model's architecture and implementation details.
(2) The paper lacks comparison with more relevant and widely-used baseline methods in the field.
(3) Although the paper constructs two explainable fake news datasets, the paper does not describe the process and criteria for creating them.

Questions To Authors And Suggestions For Rebuttal:
(1) Can the authors provide additional information on the proposed model's architecture and implementation details?
(2) Can the authors compare their proposed method with additional relevant and widely-used baseline methods in the field?
(3) Can the authors provide more details on the process and criteria for creating the two constructed explainable fake news datasets?

Overall score (1-5): 4
The paper provides an innovative approach to fake news detection using a cascade of selectors and presents two publicly available datasets for the research community. However, the paper could benefit from additional details on architectural and implementation details and comparisons with more relevant baselines.

#3 Reviewer

Overall Review:
The paper proposes a novel Coarse-to-fine Cascaded Evidence-Distillation (CofCED) neural network for explainable fake news detection. The proposed model selects the most explainable sentences for verdicts based on raw reports, thereby reducing the dependency on fact-checked reports. The paper presents two explainable fake news datasets and experimental results demonstrating that the proposed model outperforms state-of-the-art detection baselines and generates high-quality explanations.

Paper Strength:
(1) The paper addresses an important and timely problem of fake news detection and provide insights into the limitations of existing methods.
(2) The proposed CofCED model is innovative and utilizes a hierarchical encoder and cascaded selectors for selecting explainable sentences. 
(3) The paper contributes to the research community by presenting two publicly available datasets for explainable fake news detection.

Paper Weakness:
(1) The paper could benefit from more detailed clarification of the proposed model's architecture and implementation details.
(2) The paper lacks comparison with more relevant and widely-used baseline methods in the field.
(3) Although the paper constructs two explainable fake news datasets, the paper does not describe the process and criteria for creating them.

Questions To Authors And Suggestions For Rebuttal:
(1) Can the authors provide additional information on the proposed model's architecture and implementation details?
(2) Can the authors compare their proposed method with additional relevant and widely-used baseline methods in the field?
(3) Can the authors provide more details on the process and criteria for creating the two constructed explainable fake news datasets?

Overall score (1-5): 4
The paper provides an innovative approach to fake news detection using a cascade of selectors and presents two publicly available datasets for the research community. However, the paper could benefit from additional details on architectural and implementation details and comparisons with more relevant baselines.