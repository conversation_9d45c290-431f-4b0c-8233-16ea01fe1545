import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

# ===== 轨迹文件路径 =====
trajectory_files = [
    "C:/Users/<USER>/Desktop/实验数据（终）/无障碍物/CEPPO.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/无障碍物/线性熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/无障碍物/固定熵.xlsx",
    "C:/Users/<USER>/Desktop/实验数据（终）/无障碍物/PPO.xlsx",


    "C:/Users/<USER>/Desktop/实验数据（终）/预设轨迹.xlsx"
]

# ===== 图例标签 =====
labels = [
    "CEPPO",
    "Improved PPO by Linear Entropy",
    "Improved PPO by Entropy",
    "Classic PPO",
    "Preset Trajectory"
]

# ===== 每条轨迹的颜色 =====
colors = ["#e76f51", "#e8c56a", "#9b59b6", "#bfbfbf", "green"]

# ===== 图像中起点和终点的像素坐标点 =====
pixel_start = np.array([2362.04, 1123.67])
pixel_end = np.array([466.49, 220.54])

# ===== 读取背景图像 =====
img = mpimg.imread("C:/Users/<USER>/Desktop/实验数据/环境/无障碍环境.png")

# ===== 创建画布 =====
fig, ax = plt.subplots(figsize=(12, 8))
ax.imshow(img, zorder=0)

# ===== 绘制轨迹 =====
for i, file_path in enumerate(trajectory_files):
    df = pd.read_excel(file_path, header=None)
    x_world = df.iloc[:, 0].values
    y_world = df.iloc[:, 1].values

    world_start = np.array([x_world[0], y_world[0]])
    world_end = np.array([x_world[-1], y_world[-1]])

    vec_world = world_end - world_start
    vec_pixel = pixel_end - pixel_start
    angle_world = np.arctan2(vec_world[1], vec_world[0])
    angle_pixel = np.arctan2(vec_pixel[1], vec_pixel[0])
    theta = angle_pixel - angle_world
    scale = np.linalg.norm(vec_pixel) / np.linalg.norm(vec_world)

    R = np.array([
        [np.cos(theta), -np.sin(theta)],
        [np.sin(theta),  np.cos(theta)]
    ])

    transformed_points = []
    for j in range(len(x_world)):
        point = np.array([x_world[j], y_world[j]]) - world_start
        point = R @ point * scale
        point = point + pixel_start
        transformed_points.append(point)

    transformed_points = np.array(transformed_points)
    x_img = transformed_points[:, 0]
    y_img = transformed_points[:, 1]

    # 第五条轨迹使用不同样式和图层顺序
    linestyle = '--' if i == 4 else '-'
    linewidth = 2.5 if i == 4 else 1.5
    zorder = 1 if i == 4 else 2

    ax.plot(
        x_img, y_img,
        linestyle=linestyle,
        color=colors[i],
        linewidth=linewidth,
        label=labels[i],
        zorder=zorder
    )

# ===== 添加图例（右上角）=====
ax.legend(loc='upper right', fontsize=12, frameon=True)

# ===== 移除坐标轴 =====
ax.axis('off')
ax.set_aspect('equal')  # 设置等比例缩放，以免图像拉伸变形
plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
plt.margins(0)
plt.show()
